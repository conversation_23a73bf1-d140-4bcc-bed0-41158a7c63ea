<template>
    <div class="p-4">
      <el-card>
        <template #header>
          <div class="text-lg font-semibold">用户列表</div>
        </template>
  
        <el-table :data="users" stripe border style="width: 100%" v-loading="loading">
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="username" label="用户名" />
          <el-table-column prop="full_name" label="姓名" />
          <el-table-column prop="role" label="角色" width="120" />
          <el-table-column prop="registration_date" label="注册时间" width="180" />
          <el-table-column prop="last_login_at" label="上次登录时间" width="180" />
        </el-table>
  
        <div class="flex justify-end mt-4">
          <el-pagination
            v-model:current-page="page"
            :page-size="perPage"
            :total="total"
            layout="prev, pager, next"
            @current-change="fetchUsers"
          />
        </div>
      </el-card>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import userService, { type UserListItem } from '@/services/user.service'
  
  const users = ref<UserListItem[]>([])
  const loading = ref(false)
  
  const page = ref(1)
  const perPage = ref(10)
  const total = ref(0)
  
//   const fetchUsers = async () => {
//     loading.value = true
//     try {
//       const response = await userService.getUserList({
//         page: page.value,
//         per_page: perPage.value,
//       })
//       users.value = response.items
//       total.value = response.total
//     } catch (err) {
//       console.error('获取用户列表失败', err)
//     } finally {
//       loading.value = false
//     }
//   }
  
//   onMounted(() => {
//     fetchUsers()
//   });

  import {getUserList} from '@/services/userList'
//   const userList = async()=>{
//     let result = await getUserList();
//     console.log(result);
//   }
  const fetchUsers = async () => {
    loading.value = true
    try {
      const response = await getUserList()
      console.log(response);
      users.value = response.data.users; // 用户列表数据
      page.value = response.data.page; // 更新当前页码（如果后端有返回）
      perPage.value = response.data.per_page; // 更新每页条数（如果后端有返回）
      total.value = response.data.total; // 总数据条数
    } catch (err) {
      console.error('获取用户列表失败', err)
    } finally {
      loading.value = false
    }
  }
  
  onMounted(() => {
    fetchUsers()
  });
  </script>
  
  <style scoped>
  .text-lg {
    font-size: 1.125rem;
  }
  </style>
  