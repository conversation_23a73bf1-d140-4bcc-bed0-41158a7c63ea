<template>
  <div class="login-container">
    <el-card class="login-card">
      <template #header>
        <div class="login-header">
          <img src="@/assets/house.png" alt="寓见" class="login-logo">
          <h2>登录</h2>
        </div>
      </template>
      
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        label-position="top"
        @keyup.enter="handleLogin"
      >
        <el-form-item label="用户名" prop="username">
          <el-input 
            v-model="loginForm.username"
            placeholder="请输入用户名"
            prefix-icon="User"
            :maxlength="20"
            @input="handleInput"
          />
        </el-form-item>
        
        <el-form-item label="密码" prop="password">
          <el-input 
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            prefix-icon="Lock"
            show-password
            :maxlength="20"
            @input="handleInput"
          />
        </el-form-item>

        <el-form-item>
          <el-button 
            type="primary" 
            class="login-button" 
            :loading="loading"
            :disabled="!isFormValid"
            @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>

        <div class="login-links">
          <router-link to="/register">注册账号</router-link>
          <router-link to="/forgot-password">忘记密码？</router-link>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import type { FormInstance, FormRules } from 'element-plus'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const loginFormRef = ref<FormInstance>()
const loading = ref(false)
const isFormValid = ref(false)

const loginForm = reactive({
  username: '',
  password: ''
})

// 表单验证规则
const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, message: '用户名长度不能小于3个字符', trigger: 'blur' },
    { max: 20, message: '用户名长度不能超过20个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6个字符', trigger: 'blur' },
    { max: 20, message: '密码长度不能超过20个字符', trigger: 'blur' }
    // { pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{6,}$/, 
    //   message: '密码必须包含大小写字母和数字', 
    //   trigger: 'blur' 
    // }
  ]
}

// 处理输入事件
const handleInput = async () => {
  if (!loginFormRef.value) return
  await loginFormRef.value.validate((valid) => {
    isFormValid.value = valid
  })
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        await authStore.login(loginForm)
        ElMessage.success('登录成功')
        
        // 根据用户角色跳转到不同页面
        const userRole = authStore.user?.role
        let redirectPath = route.query.redirect as string
        
        if (!redirectPath) {
          switch (userRole) {
            case 'landlord':
              redirectPath = '/'
              break
            case 'tenant':
              redirectPath = '/'
              break
            case 'admin':
              redirectPath = '/'
              break
            default:
              redirectPath = '/'
          }
        }
        console.log(redirectPath)
        router.push(redirectPath)
      } catch (error: any) {
        ElMessage.error(error.response?.data?.error || '登录失败，请检查用户名和密码')
      } finally {
        loading.value = false
      }
    }
  })
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
}

.login-card {
  width: 400px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
}

.login-logo {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
}

.login-button {
  width: 100%;
  height: 40px;
  font-size: 16px;
}

.login-links {
  margin-top: 16px;
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.login-links a {
  color: #409EFF;
  text-decoration: none;
}

.login-links a:hover {
  text-decoration: underline;
}

:deep(.el-input__wrapper) {
  padding: 1px 15px;
}

:deep(.el-input__inner) {
  height: 40px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  padding-bottom: 8px;
}
</style>