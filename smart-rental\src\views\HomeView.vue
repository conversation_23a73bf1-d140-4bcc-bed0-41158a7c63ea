<template>
  <!-- <DefaultLayout> -->
    <div class="home-container">
      <h2>欢迎来到租房系统</h2>
      <div class="content">
        <lunbotu />
        <showHouse8 />
        <showHouse />
      </div>
    </div>
  <!-- </DefaultLayout> -->
</template>

<script setup>
import DefaultLayout from '@/layouts/DefaultLayout.vue'
import { ref } from 'vue'
import lunbotu from '@/components/house/lunbotu.vue'
import showHouse8 from '@/components/house/showHouse8.vue'
import showHouse from '@/components/house/showHouse.vue'
import { useAuthStore } from '@/stores/auth'
const houses = ref([
  { id: 1, title: '北京三居室', price: 4200 },
  { id: 2, title: '上海单间', price: 3500 },
])
const authStore = useAuthStore()
if (!authStore.isLoggedIn) {
  console.log('未登录')
}else {
  console.log('已登录')
}
</script>

<style scoped>
.home-container {
  height: 100%;
  width: 100%;
}

h2 {
  margin: 0;
  padding: 20px 0;
  text-align: center;
}

.content {
  height: calc(100% - 60px);
  width: 100%;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>
