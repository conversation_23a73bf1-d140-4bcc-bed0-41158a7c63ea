import api from '../util/api'

export interface SystemSettings {
  site_name: string
  site_description: string
  contact_email: string
  contact_phone: string
  maintenance_mode: boolean
  registration_enabled: boolean
  max_upload_size: number
  allowed_file_types: string[]
}

export interface AuditLog {
  id: number
  user_id: number
  action: string
  resource_type: string
  resource_id: number
  details: string
  ip_address: string
  user_agent: string
  created_at: string
  user: {
    id: number
    username: string
    full_name: string
  }
}

export interface SystemInfo {
  version: string
  database_status: string
  storage_usage: number
  total_storage: number
  active_users: number
  system_uptime: string
}

export interface MessageResponse {
  message: string
}

class AdminService {
  /**
   * 获取系统设置
   */
  async getSystemSettings(): Promise<SystemSettings> {
    const response = await api.get<SystemSettings>('/admin/settings')
    return response.data
  }

  /**
   * 更新系统设置
   * @param settings 系统设置
   */
  async updateSystemSettings(settings: Partial<SystemSettings>): Promise<MessageResponse> {
    const response = await api.put<MessageResponse>('/admin/settings', settings)
    return response.data
  }

  /**
   * 获取审计日志
   * @param params 查询参数
   */
  async getAuditLogs(params?: {
    user_id?: number
    action?: string
    resource_type?: string
    start_date?: string
    end_date?: string
  }): Promise<AuditLog[]> {
    const response = await api.get<AuditLog[]>('/admin/audit-logs', { params })
    return response.data
  }

  /**
   * 获取系统信息
   */
  async getSystemInfo(): Promise<SystemInfo> {
    const response = await api.get<SystemInfo>('/admin/system-info')
    return response.data
  }

  /**
   * 清理系统缓存
   */
  async clearCache(): Promise<MessageResponse> {
    const response = await api.post<MessageResponse>('/admin/clear-cache')
    return response.data
  }

  /**
   * 备份数据库
   */
  async backupDatabase(): Promise<MessageResponse> {
    const response = await api.post<MessageResponse>('/admin/backup-database')
    return response.data
  }

  /**
   * 发送系统通知
   * @param notification 通知内容
   */
  async sendSystemNotification(notification: {
    title: string
    content: string
    target_users?: 'all' | 'landlords' | 'tenants'
  }): Promise<MessageResponse> {
    const response = await api.post<MessageResponse>('/admin/notifications', notification)
    return response.data
  }

  /**
   * 激活/禁用用户
   * @param userId 用户ID
   * @param active 是否激活
   */
  async activateUser(userId: number, active: boolean): Promise<MessageResponse> {
    const response = await api.patch<MessageResponse>(`/admin/users/activate/${userId}`, { active })
    return response.data
  }

  /**
   * 更新用户角色
   * @param userId 用户ID
   * @param role 新角色
   */
  async updateUserRole(userId: number, role: string): Promise<MessageResponse> {
    const response = await api.patch<MessageResponse>(`/admin/users/role/${userId}`, { role })
    return response.data
  }

  /**
   * 获取待审核房源
   */
  async getPendingHouses(): Promise<any[]> {
    const response = await api.get<any[]>('/admin/houses/pending')
    return response.data
  }

  /**
   * 验证房源
   * @param houseId 房源ID
   * @param verified 是否验证通过
   * @param notes 备注
   */
  async verifyHouse(houseId: number, verified: boolean, notes?: string): Promise<MessageResponse> {
    const response = await api.patch<MessageResponse>(`/admin/houses/verify/${houseId}`, { verified, notes })
    return response.data
  }
}

export default new AdminService()
