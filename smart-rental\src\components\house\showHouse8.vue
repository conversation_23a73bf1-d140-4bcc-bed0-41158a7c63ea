<template>
    <div class="recommend-list">
    <a href="" target="_blank" class="recommend-list-item">
        <div class="img">
            <img alt="新世界广场" class="item-image" src="@/assets/img/wallhaven-6k3oox.jpg" loading="lazy">
        </div>
        <div class="item-info">
            <div class="item-info-title">新世界广场</div>
            <div class="item-info-meta">
                合租<span class="seperate"></span><span>1室1厅 25㎡</span>
            </div>
            <div class="item-info-price">
                780<span class="unit">元/月</span>
            </div>
        </div>
    </a>
    <a href="https://cs.zu.anjuke.com/fangyuan/4057334243204102?from=HomePage_Renting" target="_blank" class="recommend-list-item">
        <div class="img">
            <img alt="华银园" class="item-image" src="@/assets/img/wallhaven-6k3oox.jpg" loading="lazy">
        </div>
        <div class="item-info">
            <div class="item-info-title">华银园</div>
            <div class="item-info-meta">
                合租<span class="seperate"></span><span>1室1厅 20㎡</span>
            </div>
            <div class="item-info-price">
                550<span class="unit">元/月</span>
            </div>
        </div>
    </a>
    <a href="" target="_blank" class="recommend-list-item">
        <div class="img">
            <img alt="万科魅力之城" class="item-image" src="@/assets/img/wallhaven-6k3oox.jpg" loading="lazy">
        </div>
        <div class="item-info">
            <div class="item-info-title">万科魅力之城</div>
            <div class="item-info-meta">
                整租<span class="seperate"></span><span>4室2厅 142.63㎡</span>
            </div>
            <div class="item-info-price">
                4200<span class="unit">元/月</span>
            </div>
        </div>
    </a>
    <a href="" target="_blank" class="recommend-list-item">
        <div class="img">
            <img alt="高岭小区" class="item-image" src="@/assets/img/wallhaven-6k3oox.jpg" loading="lazy">
        </div>
        <div class="item-info">
            <div class="item-info-title">高岭小区</div>
            <div class="item-info-meta">
                整租<span class="seperate"></span><span>1室1厅 33㎡</span>
            </div>
            <div class="item-info-price">
                1080<span class="unit">元/月</span>
            </div>
        </div>
    </a>
    <a href="" target="_blank" class="recommend-list-item">
        <div class="img">
            <img alt="南庭城果" class="item-image" src="@/assets/img/wallhaven-6k3oox.jpg" loading="lazy">
        </div>
        <div class="item-info">
            <div class="item-info-title">南庭城果</div>
            <div class="item-info-meta">
                整租<span class="seperate"></span><span>1室1厅 59㎡</span>
            </div>
            <div class="item-info-price">
                950<span class="unit">元/月</span>
            </div>
        </div>
    </a>
    <a href="" target="_blank" class="recommend-list-item">
        <div class="img">
            <img alt="三重星都心" class="item-image" src="@/assets/img/wallhaven-6k3oox.jpg" loading="lazy">
        </div>
        <div class="item-info">
            <div class="item-info-title">三重星都心</div>
            <div class="item-info-meta">
                合租<span class="seperate"></span><span>1室0厅 10㎡</span>
            </div>
            <div class="item-info-price">
                550<span class="unit">元/月</span>
            </div>
        </div>
    </a>
    <a href="" target="_blank" class="recommend-list-item">
        <div class="img">
            <img alt="上河国际商业广场国际公寓" class="item-image" src="@/assets/img/wallhaven-6k3oox.jpg" loading="lazy">
        </div>
        <div class="item-info">
            <div class="item-info-title">上河国际商业广场国际公寓</div>
            <div class="item-info-meta">
                合租<span class="seperate"></span><span>1室0厅 22㎡</span>
            </div>
            <div class="item-info-price">
                800<span class="unit">元/月</span>
            </div>
        </div>
    </a>
    <a href="" target="_blank" class="recommend-list-item">
        <div class="img">
            <img alt="洞井商贸城" class="item-image" src="@/assets/img/wallhaven-6k3oox.jpg" loading="lazy">
        </div>
        <div class="item-info">
            <div class="item-info-title">洞井商贸城</div>
            <div class="item-info-meta">
                整租<span class="seperate"></span><span>1室1厅 40㎡</span>
            </div>
            <div class="item-info-price">
                700<span class="unit">元/月</span>
            </div>
        </div>
    </a>
</div>
</template>
<style scoped>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f0f2f5;
}

.recommend-list {
    display: grid;
    grid-template-columns: repeat(4, 1fr); /* 4 columns */
    gap: 20px; /* Space between items */
    max-width: 1200px; /* Adjust as needed */
    margin: 0 auto;
}

.recommend-list-item {
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-decoration: none;
    color: inherit;
    transition: transform 0.2s ease-in-out;
}

.recommend-list-item:hover {
    transform: translateY(-5px);
}

.recommend-list-item .img {
    width: 100%;
    height: 150px; /* Fixed height for images */
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
}

.recommend-list-item .item-image {
    width: 100%;
    height: 100%;
    object-fit: cover; /* Ensures the image covers the area, cropping if necessary */
}

.item-info {
    padding: 10px 15px;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.item-info-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.item-info-meta {
    font-size: 13px;
    color: #666;
    display: flex;
    align-items: center;
}

.item-info-meta .seperate {
    display: inline-block;
    width: 1px;
    height: 10px;
    background-color: #ccc;
    margin: 0 8px;
    vertical-align: middle;
}

.item-info-price {
    font-size: 18px;
    font-weight: bold;
    color: #ff6600; /* Orange color for price */
}

.item-info-price .unit {
    font-size: 13px;
    font-weight: normal;
    margin-left: 2px;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    .recommend-list {
        grid-template-columns: repeat(3, 1fr); /* 3 columns on smaller screens */
    }
}

@media (max-width: 768px) {
    .recommend-list {
        grid-template-columns: repeat(2, 1fr); /* 2 columns on tablets */
    }
}

@media (max-width: 480px) {
    .recommend-list {
        grid-template-columns: 1fr; /* 1 column on mobile phones */
    }
}
</style>
