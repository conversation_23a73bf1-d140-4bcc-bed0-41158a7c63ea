import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import authService from '@/services/auth.service'
import type { LoginRequest, RegisterRequest } from '@/services/auth.service'
import { ElMessage } from 'element-plus'

export interface User {
  id: number
  username: string
  role: string
  email?: string
  full_name?: string
}

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null as User | null,
    token: null as string | null,
    loading: false
  }),

  getters: {
    isLoggedIn: (state) => !!state.token,
    isLandlord: (state) => state.user?.role === 'landlord',
    isTenant: (state) => state.user?.role === 'tenant',
    isAdmin: (state) => state.user?.role === 'admin',
  },

  actions: {
    initialize() {
      const storedToken = localStorage.getItem('token')
      const storedUser = localStorage.getItem('user')

      if (storedToken) {
        this.token = storedToken
      }else{
        console.log('No token found in localStorage')
      }

      if (storedUser) {
        try {
          this.user = JSON.parse(storedUser)
        } catch (error) {
          console.error('Failed to parse user from localStorage', error)
          localStorage.removeItem('user')
        }
      }else{
        console.log('No user found in localStorage')
      }
    },

    async login(loginData: LoginRequest) {
      this.loading = true
      try {
        const response = await authService.login(loginData)
        console.log('Login response:', response)
        this.setToken(response.access_token)
        this.setUser(response.user)
        // this.token = response.access_token
        // this.user = {
        //   id: response.id,
        //   username: response.username,
        //   role: response.role,
        // }

        // localStorage.setItem('token', this.token)
        // localStorage.setItem('user', JSON.stringify(this.user))

        //ElMessage.success('登录成功')

        const role = response.user.role
        // if (role === 'landlord') {
        //   window.location.href = '/landlord/dashboard'
        // } else if (role === 'tenant') {
        //   window.location.href = '/tenant/dashboard'
        // } else if (role === 'admin') {
        //   window.location.href = '/admin/dashboard'
        // } else {
        //   window.location.href = '/'
        // }
        //window.location.href = '/'

        return response
      } catch (error) {
        console.error('Login error:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    async register(registerData: RegisterRequest) {
      this.loading = true
      try {
        const response = await authService.register(registerData)

        // this.token = response.token
        // this.user = {
        //   id: response.id,
        //   username: response.username,
        //   email: response.email,
        //   role: response.role,
        // }

        // localStorage.setItem('token', this.token)
        // localStorage.setItem('user', JSON.stringify(this.user))

        ElMessage.success('注册成功')

        // if (response.user.role === 'landlord') {
        //   window.location.href = '/landlord/dashboard'
        // } else if (response.user.role === 'tenant') {
        //   window.location.href = '/tenant/dashboard'
        // } else {
        //   window.location.href = '/'
        // }

        return response
      } catch (error) {
        console.error('Register error:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    async logout() {
      this.loading = true
      try {
        await authService.logout()
      } catch (error) {
        console.error('Logout error:', error)
      } finally {
        this.clearAuth()
        ElMessage.success('已成功退出登录')
        window.location.href = '/login'
      }
    },

    async refreshToken() {
      try {
        const response = await authService.refreshToken()
        this.token = response.token
        localStorage.setItem('token', this.token)
        return response
      } catch (error) {
        console.error('Refresh token error:', error)
        throw error
      }
    },

    setUser(userData: User) {
      this.user = userData
      localStorage.setItem('user', JSON.stringify(userData))
    },

    setToken(tokenValue: string) {
      this.token = tokenValue
      localStorage.setItem('token', tokenValue)
    },

    clearAuth() {
      this.user = null
      this.token = null
      localStorage.removeItem('user')
      localStorage.removeItem('token')
    },
  }
})
