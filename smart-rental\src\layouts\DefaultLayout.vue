<script setup lang="ts">
import { ref } from 'vue'
import MenuLeft from '@/components/layout/Menu-left.vue'
import MenuTop from '@/components/layout/Menu-top.vue'
import { useAuthStore } from '@/stores/auth.ts'
import { watch } from 'vue'
const menuLeftRef = ref()
const isMenuCollapsed = ref(false)
const authStore = useAuthStore()
const handleToggleMenu = (collapsed: boolean) => {
  isMenuCollapsed.value = collapsed
  if (menuLeftRef.value) {
    menuLeftRef.value.isCollapse = collapsed
  }
}
</script>

<template>
  <div class="layout-wrapper">
    <div class="layout-container" :class="{ 'menu-collapsed': isMenuCollapsed }">
      <MenuLeft ref="menuLeftRef" class="layout-left" />
      <MenuTop :is-logged-in="authStore.isLoggedIn" @toggle-menu="handleToggleMenu" class="layout-top" />
      <div class="layout-main">
        <!-- <slot /> -->
        <router-view />
      </div>
    </div>
  </div>
</template>

<style scoped>
.layout-container[data-v-75d0be1e] {
    display: grid;
    width: 100%;
    height: 100vh;
    grid-template-areas:
        "left top"
        "left main";
    grid-template-columns: var(--menu-width, 200px) 1fr;
    grid-template-rows: var(--header-height, 60px) 1fr;
}

.layout-container.menu-collapsed {
  grid-template-columns: var(--menu-collapsed-width, 64px) 1fr;
}

.layout-left {
  grid-area: left;
  height: 100vh;
}

.layout-top {
  grid-area: top;
  width: 100%;
}

.layout-main {
  grid-area: main;
  width: 100%;
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
  background-color: #f5f7fa;
  overflow-y: auto;
}

/* CSS 变量定义 */
:root {
  --menu-width: 200px;
  --menu-collapsed-width: 64px;
  --header-height: 60px;
}
</style>
  
  
  