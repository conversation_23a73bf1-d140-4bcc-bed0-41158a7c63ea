import api from '../util/api'

export interface Lease {
  id: number
  house: {
    id: number
    title: string
    address: string
    city: string
    district: string
    main_image_url: string
  }
  tenant: {
    id: number
    username: string
    full_name: string
    phone_number: string
  }
  landlord: {
    id: number
    username: string
    full_name: string
    phone_number: string
  }
  start_date: string
  end_date: string
  rent_amount_monthly: number
  deposit_amount: number
  payment_day_of_month: number
  contract_terms: string
  contract_document_url: string | null
  status: string
  created_at: string
  updated_at: string
}

export interface CreateLeaseRequest {
  house_id: number
  tenant_id: number
  start_date: string
  end_date: string
  rent_amount_monthly: number
  deposit_amount: number
  payment_day_of_month: number
  contract_terms: string
}

export interface TerminateLeaseRequest {
  reason: string
}

export interface MessageResponse {
  message: string
}

class LeaseService {
  /**
   * 获取租约列表
   * @param params 查询参数
   */
  async getLeaseList(params?: {
    status?: string
    house_id?: number
    tenant_id?: number
  }): Promise<Lease[]> {
    const response = await api.get<Lease[]>('/leases', { params })
    return response.data
  }

  /**
   * 获取租约详情
   * @param leaseId 租约ID
   */
  async getLeaseDetail(leaseId: number): Promise<Lease> {
    const response = await api.get<Lease>(`/leases/${leaseId}`)
    return response.data
  }

  /**
   * 创建租约 (房东)
   * @param data 租约信息
   */
  async createLease(data: CreateLeaseRequest): Promise<{ id: number; status: string; message: string }> {
    const response = await api.post<{ id: number; status: string; message: string }>('/leases', data)
    return response.data
  }

  /**
   * 签署租约 (租客)
   * @param leaseId 租约ID
   */
  async signLease(leaseId: number): Promise<{ id: number; status: string; message: string }> {
    const response = await api.post<{ id: number; status: string; message: string }>(`/leases/${leaseId}/sign`)
    return response.data
  }

  /**
   * 上传合同文档 (房东)
   * @param leaseId 租约ID
   * @param file 合同文件
   */
  async uploadContractDocument(leaseId: number, file: File): Promise<{ contract_document_url: string }> {
    const formData = new FormData()
    formData.append('document', file)

    const response = await api.post<{ contract_document_url: string }>(`/leases/${leaseId}/document`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    return response.data
  }

  /**
   * 终止租约 (房东)
   * @param leaseId 租约ID
   * @param data 终止原因
   */
  async terminateLease(leaseId: number, data: TerminateLeaseRequest): Promise<{ id: number; status: string; message: string }> {
    const response = await api.post<{ id: number; status: string; message: string }>(`/leases/${leaseId}/terminate`, data)
    return response.data
  }
}

export default new LeaseService()
