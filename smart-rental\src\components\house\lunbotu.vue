<template>
  <el-carousel indicator-position="outside" height="400px" :interval="4000">
    <el-carousel-item v-for="item in mediaList" :key="item.id">
      <!-- 图片展示 -->
      <img v-if="item.type === 'image'" :src="item.url" :alt="item.title" class="media-item">
      <!-- 视频展示 -->
      <video 
        v-else-if="item.type === 'video'" 
        :src="item.url" 
        class="media-item" 
        controls
        @mouseenter="pauseCarousel"
        @mouseleave="startCarousel"
      >
        您的浏览器不支持视频播放
      </video>
    </el-carousel-item>
  </el-carousel>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 媒体列表数据
const mediaList = ref([
  {
    id: 1,
    type: 'image',
    url: '/src/assets/img/old_bridge_by_arsenixc-d8mc1rg.jpg',
    title: '房屋图片1'
  },
  {
    id: 2,
    type: 'image',
    url: '/src/assets/house/house2.jpg',
    title: '房屋图片2'
  },
  {
    id: 3,
    type: 'video',
    url: '/src/assets/videos/房屋租赁系统 - 个人 - Microsoft​ Edge 2025-05-23 22-40-07.mp4',
    title: '房屋视频介绍'
  },
  {
    id: 4,
    type: 'image',
    url: '/src/assets/house/house3.jpg',
    title: '房屋图片3'
  }
])

// 获取 carousel 实例
const carousel = ref()

// 暂停轮播
const pauseCarousel = () => {
  carousel.value?.pause()
}

// 开始轮播
const startCarousel = () => {
  carousel.value?.start()
}
</script>

<style scoped>
.media-item {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.el-carousel {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.el-carousel__item {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
}

/* 视频控制样式 */
video.media-item {
  object-fit: contain;
  background-color: #000;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .el-carousel {
    height: 300px;
  }
}
</style>
  