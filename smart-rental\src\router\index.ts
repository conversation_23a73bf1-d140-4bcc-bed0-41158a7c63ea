// 修改路由守卫中的登录判断
import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '@/views/HomeView.vue'
import { useAuthStore } from '@/stores/auth' // 导入 authStore
import { ElMessage } from 'element-plus'
import authService from '@/services/auth.service'
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'layout',
      component:()=> import('@/layouts/DefaultLayout.vue'),
      meta: { title: '首页' },
      redirect: '/home',
      children: [
        {path: 'demo',name: 'demo',component: () => import('@/views/Demo.vue')},
        {path: 'home',name: 'home',component: HomeView},
        {path: 'lunbotu',name: 'lunbotu',component: () => import('@/components/house/lunbotu.vue')},
        {path: 'user',name: 'user',component: () => import('@/views/UserList.vue')},
        {path: 'search',name: 'search',component: () => import('@/views/search.vue')},
      ]
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('@/views/LoginView.vue'),
      meta: { 
        title: '登录',
        guest: true // 仅允许未登录用户访问
      }
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('@/views/RegisterView.vue'),
      meta: { 
        title: '注册',
        guest: true // 仅允许未登录用户访问
      }
    }
  ]
})

// 全局前置守卫
router.beforeEach((to, _from, next) => {
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 智能房屋租赁系统` : '智能房屋租赁系统'

  const authStore = useAuthStore() // 使用 authStore
  const isLoggedIn = authStore.isLoggedIn // 使用 authStore 的计算属性
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  const isGuestOnly = to.matched.some(record => record.meta.guest)

  // 获取当前用户角色
  const currentUser = authService.getCurrentUser()
  const userRole = currentUser?.role

  // 检查路由是否需要特定角色
  const requiredRole = to.matched.find(record => record.meta.role)?.meta.role

  if (requiresAuth && !isLoggedIn) {
    // 需要登录但未登录，重定向到登录页
    ElMessage.warning('请先登录')
    next({ name: 'login', query: { redirect: to.fullPath } })
  } else if (isGuestOnly && isLoggedIn) {
    // 仅限游客但已登录，重定向到首页
    next({ name: 'home' })
  } else if (requiresAuth && requiredRole && userRole !== requiredRole) {
    // 需要特定角色但角色不匹配，重定向到首页
    ElMessage.error('您没有权限访问该页面')
    next({ name: 'home' })
  } else {
    // 其他情况正常通过
    next()
  }
})

export default router
