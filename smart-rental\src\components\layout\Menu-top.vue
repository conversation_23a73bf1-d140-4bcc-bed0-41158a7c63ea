<template>
  <div class="top-menu-container" :class="{ 'menu-collapsed': isLeftMenuCollapsed }">
    <el-menu
      :default-active="activeIndex"
      class="el-menu-top"
      mode="horizontal"
      background-color="#fff"
      text-color="#303133"
      active-text-color="#409EFF"
      @select="handleSelect"
    >
      <el-menu-item index="0" @click="toggleLeftMenu">
        <el-icon>
          <component :is="isLeftMenuCollapsed ? 'Expand' : 'Fold'" />
        </el-icon>
      </el-menu-item>
      <el-menu-item index="1">
        <el-icon><House /></el-icon>
        首页
      </el-menu-item>
      <el-menu-item index="2">
        <el-icon><Search /></el-icon>
        找房
      </el-menu-item>

      <!-- 已登录状态 -->
      <template v-if="authStore.isLoggedIn || isLoggedIn">
        <el-menu-item index="3">
          <el-icon><Bell /></el-icon>
          消息
          <el-badge :value="3" class="notice-badge" />
        </el-menu-item>
        <el-dropdown trigger="click">
          <div class="user-info">
            <el-avatar 
              :size="40" 
              :src="userAvatar"
              @error="handleAvatarError"
            />
            <span class="username">{{ authStore.user?.full_name || '用户' }}</span>
            <el-icon class="el-icon--right"><CaretBottom /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>
                <el-icon><User /></el-icon>
                个人中心
              </el-dropdown-item>
              <el-dropdown-item>
                <el-icon><Setting /></el-icon>
                账号设置
              </el-dropdown-item>
              <el-dropdown-item divided @click="handleLogout">
                <el-icon><SwitchButton /></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>

      <!-- 未登录状态 -->
      <template v-else>
        <div class="auth-buttons">
          <el-button type="primary" @click="handleLogin">登录</el-button>
          <el-button @click="handleRegister">注册</el-button>
        </div>
      </template>
    </el-menu>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { 
  Expand, 
  Fold,
  House,
  Search,
  Bell,
  User,
  Setting,
  SwitchButton,
  CaretBottom
} from '@element-plus/icons-vue'

defineProps({
  isLoggedIn: {
    type: Boolean,
    default: false
  }
})
const router = useRouter()
const authStore = useAuthStore()
const activeIndex = ref('1')
const isLeftMenuCollapsed = ref(false)
const userAvatar = ref('https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png')

const handleSelect = (key: string, keyPath: string[]) => {
  console.log(key, keyPath)
}

const toggleLeftMenu = () => {
  isLeftMenuCollapsed.value = !isLeftMenuCollapsed.value
  emit('toggle-menu', isLeftMenuCollapsed.value)
}

const handleAvatarError = () => {
  userAvatar.value = 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
}

const handleLogin = () => {
  router.push('/login')
}

const handleRegister = () => {
  router.push('/register')
}

const handleLogout = async () => {
  await authStore.logout()
}

const emit = defineEmits(['toggle-menu'])
</script>

<style scoped>
.top-menu-container {
  position: fixed;
  top: 0;
  left: var(--menu-width, 200px);
  right: 0;
  z-index: 1000;
  transition: left 0.3s;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
}

.top-menu-container.menu-collapsed {
  left: var(--menu-collapsed-width, 64px);
}

.el-menu-top {
  width: 100%;
  height: var(--header-height, 60px);
  display: flex;
  align-items: center;
}

:deep(.el-menu) {
  border-bottom: none;
}

:deep(.el-menu-item) {
  display: flex;
  align-items: center;
  height: var(--header-height, 60px);
  line-height: var(--header-height, 60px);
  font-size: 14px;
}

:deep(.el-menu-item .el-icon) {
  margin-right: 6px;
  width: 20px;
  height: 20px;
}

.user-section {
  margin-left: auto;
  padding-right: 20px;
}

.auth-buttons {
  margin-left: auto;
  padding-right: 20px;
  display: flex;
  gap: 10px;
  align-items: center;
}

.auth-buttons .el-button {
  margin-left: 10px;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0 10px;
  height: 100%;
  margin-left: auto;
  padding-right: 20px;
}

.username {
  margin: 0 8px;
  font-size: 14px;
  color: #606266;
}

.notice-badge {
  margin-top: -10px;
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  padding: 8px 20px;
}

:deep(.el-dropdown-menu__item .el-icon) {
  margin-right: 10px;
  width: 16px;
  height: 16px;
}

.user-info:hover {
  background-color: #f5f7fa;
}

:deep(.el-menu-item:not(.is-disabled):hover) {
  background-color: #f5f7fa !important;
}
</style>
  