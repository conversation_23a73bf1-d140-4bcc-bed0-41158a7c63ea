<template>
    <div class="property-list">
        <div class="property">
            <a href="#" class="property-ex">
                <div class="property-image">
                    <img src="@/assets/img/wallhaven-6k3oox.jpg" alt="Property Image" class="lazy-img cover">
                </div>
                <div class="property-content">
                    <div class="property-content-detail">
                        <div class="property-content-title">
                            <img src="https://pages.anjukestatic.com/usersite/app/anjukeapp/<EMAIL>" class="property-content-title-anxuan" alt="人气好房">
                            <h3 class="property-content-title-name" title="开福区江景奢宅 新科技住宅 地理位置优越">开福区江景奢宅 新科技住宅 地理位置优越</h3>
                        </div>
                        <section>
                            <div class="property-content-info">
                                <p class="property-content-info-text property-content-info-attribute">
                                    <span>4</span> <span>室</span> <span>2</span> <span>厅</span> <span>3</span> <span>卫</span>
                                </p>
                                <p class="property-content-info-text">213㎡</p>
                                <p class="property-content-info-text">西</p>
                            </div>
                            <div class="property-content-info property-content-info-comm">
                                <p class="property-content-info-comm-name">湘江金茂府</p>
                                <p class="property-content-info-comm-address">
                                    <span>开福</span><span>开福寺</span><span>开福区湘江北路与凤嘴路交汇处</span>
                                </p>
                            </div>
                            <div class="property-content-info">
                                <span class="property-content-info-tag">厨卫全明</span>
                                <span class="property-content-info-tag">大阳台</span>
                                <span class="property-content-info-tag">主卧套房</span>
                            </div>
                        </section>
                    </div>
                    <div class="property-price">
                        <p class="property-price-total">
                            <span class="property-price-total-num">554</span> <span class="property-price-total-text">万</span>
                        </p>
                        <p class="property-price-average">26010元/㎡</p>
                    </div>
                </div>
            </a>
        </div>

        <div class="property">
            <a href="#" class="property-ex">
                <div class="property-image">
                    <img src="@/assets/img/wallhaven-6k3oox.jpg" alt="Property Image" class="lazy-img cover">
                </div>
                <div class="property-content">
                    <div class="property-content-detail">
                        <div class="property-content-title">
                            <img src="https://pages.anjukestatic.com/usersite/app/anjukeapp/<EMAIL>" class="property-content-title-anxuan" alt="人气好房">
                            <h3 class="property-content-title-name" title="明昇壹城一期业主自住精装大三房 正地铁...">明昇壹城一期业主自住精装大三房 正地铁...</h3>
                        </div>
                        <section>
                            <div class="property-content-info">
                                <p class="property-content-info-text property-content-info-attribute">
                                    <span>3</span> <span>室</span> <span>2</span> <span>厅</span> <span>2</span> <span>卫</span>
                                </p>
                                <p class="property-content-info-text">125.05㎡</p>
                                <p class="property-content-info-text">南</p>
                                <p class="property-content-info-text">高层(共18层)</p>
                                <p class="property-content-info-text">2018年建造</p>
                            </div>
                            <div class="property-content-info property-content-info-comm">
                                <p class="property-content-info-comm-name">明昇壹城</p>
                                <p class="property-content-info-comm-address">
                                    <span>雨花</span><span>体育新城</span><span>劳动东路821号</span>
                                </p>
                            </div>
                            <div class="property-content-info">
                                <span class="property-content-info-tag">南</span>
                                <span class="property-content-info-tag">满五年</span>
                                <span class="property-content-info-tag">近地铁</span>
                                <span class="property-content-info-tag">绿化率高</span>
                            </div>
                        </section>
                    </div>
                    <div class="property-price">
                        <p class="property-price-total">
                            <span class="property-price-total-num">185</span> <span class="property-price-total-text">万</span>
                        </p>
                        <p class="property-price-average">14795元/㎡</p>
                    </div>
                </div>
            </a>
        </div>
    </div>
</template>
<style scoped>
body {
    font-family: Arial, sans-serif;
    background-color: #f5f5f5;
    margin: 20px;
}

.property-list {
    max-width: 1410px;
    margin: 0 auto;
}

.property {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden; /* Ensures border-radius applies to children */
}

.property-ex {
    display: flex;
    text-decoration: none;
    color: inherit;
}

.property-image {
    width: 200px; /* Fixed width for the image */
    height: 150px; /* Fixed height for the image */
    flex-shrink: 0; /* Prevent image from shrinking */
    position: relative; /* For VR tag positioning */
}

.property-image img {
    width: 100%;
    height: 100%;
    object-fit: cover; /* Ensures the image covers the area without distortion */
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
}

.vr-tag {
    position: absolute;
    bottom: 8px;
    left: 8px;
    background-color: rgba(0, 0, 0, 0.6);
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.property-content {
    flex-grow: 1; /* Allows content to take remaining space */
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start; /* Align content to the top */
}

.property-content-detail {
    flex-grow: 1;
    margin-right: 15px; /* Space between content and price */
}

.property-content-title {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.property-content-title-anxuan {
    width: 74px; /* Based on the image's intrinsic size */
    height: 22px; /* Based on the image's intrinsic size */
    margin-right: 8px;
    flex-shrink: 0; /* Prevent shrinking */
}

.property-content-title-name {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin: 0;
    white-space: nowrap; /* Prevent title from wrapping */
    overflow: hidden;
    text-overflow: ellipsis; /* Add ellipsis if text overflows */
    max-width: calc(100% - 120px); /* Adjust based on icon width */
}

.property-content-title-othertag {
    margin-left: auto; /* Pushes other tags to the right */
    display: flex;
    align-items: center;
}

.property-content-title-othertag-content {
    display: flex;
    gap: 5px; /* Space between the ad and new tags */
}

.property-content-title-othertag-youpin {
    height: 24px;
    width: auto; /* Adjust width proportionally */
}


.property-content-info {
    display: flex;
    flex-wrap: wrap; /* Allow items to wrap if needed */
    margin-bottom: 5px;
    font-size: 14px;
    color: #666;
}

.property-content-info-text {
    margin-right: 15px;
    white-space: nowrap;
}

.property-content-info-attribute span {
    margin-right: 2px;
}

.property-content-info-comm {
    margin-top: 10px;
    margin-bottom: 10px;
}

.property-content-info-comm-name {
    font-weight: bold;
    margin-right: 10px;
}

.property-content-info-comm-address {
    color: #999;
}

.property-content-info-tag {
    background-color: #f3f5fb;
    color: #667D91;
    padding: 4px 8px;
    border-radius: 4px;
    margin-right: 8px;
    margin-bottom: 5px; /* For wrapped tags */
    font-size: 12px;
    white-space: nowrap;
}

.property-price {
    text-align: right;
    flex-shrink: 0; /* Prevent price from shrinking */
}

.property-price-total {
    font-size: 24px;
    color: #ff5a5f; /* Red color */
    font-weight: bold;
    margin-bottom: 5px;
}

.property-price-total-text {
    font-size: 14px;
}

.property-price-average {
    font-size: 14px;
    color: #999;
}
</style>
