import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      'vue': 'vue/dist/vue.esm-bundler.js'
    },
  },
  server: {
    proxy: {
      '/api': {//获取路径包含了/api的请求
        target: 'http://localhost:5000',//后台服务的地址
        changeOrigin: true,//修改源
        rewrite: (path) => path.replace(/^\/api/, '')//重写路径，把路径中/api替换成''
      }
    }
  }
})
