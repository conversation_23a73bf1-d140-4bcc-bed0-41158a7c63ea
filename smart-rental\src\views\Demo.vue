<template>
    <div class="wrapper">
        <h1 class="house-title">
            <div class="strongbox tit-rest">精装一室 汽车南站 德思勤商圈 邓家湾 押一付一 无需登记</div>
            <div class="house-title-tag-box">
                <em class="anxvan-ico">安选</em>
                <img class="dzrq-icon" src="//pic8.58cdn.com.cn/nowater/frs/n_v3518537ce68ed4c778c04c9d76e7d4f98.png" alt="人气好房">
            </div>
        </h1>
        <div class="title-basic-info clearfix">
            <span class="light info-tag"><em><b class="strongbox" style="font-weight: normal;">500</b></em>元/月</span>
            <span class="info-tag">
                <em><b class="strongbox" style="font-weight: normal;">1</b></em>室<em><b class="strongbox" style="font-weight: normal;">1</b></em>厅
            </span>
            <span class="info-tag no-line"><em><b class="strongbox" style="font-weight: normal;">10.00</b></em>平方米</span>
            <ul class="title-label cf">
                <li class="title-label-item rent">合租</li>
                <li class="title-label-item buy">朝南</li>
                <li class="title-label-item subway"></li>
            </ul>
            <a class="report-btn" href="//apirent.anjuke.com/rentercenter/Report/detail_app_v2?infoId=4081555902163980&amp;plat=1&amp;from=ajkpcdetail" target="_blank">
                <i class="report-btn-icon"></i>
                <p>举报</p>
            </a>
            <div class="report-phone">
                <p>举报电话</p>
                <p>400-6209008</p>
            </div>
        </div>
        <div class="mainbox cf">
            <div class="lbox">
                <div class="switch_warpper">
                    <div class="switch_with_map">
                        <div class="inner_switcher" id="inner_switcher">
                            <div class="switch_mask switch_left_mask" id="switch_left_mask" data-track="pc_fydy_switch_qh">
                                <i class="switch_left_btn switch_btn iconfont" id="switch_left_btn"></i>
                                <span id="prev-title">户型图片</span>
                            </div>
                            <div class="switch_mask switch_right_mask" id="switch_right_mask" data-track="pc_fydy_switch_qh">
                                <i class="switch_right_btn switch_btn iconfont" id="switch_right_btn"></i>
                                <span id="next-title">室内图片</span>
                            </div>
                            <div class="switch_wrap" id="switch_wrap" style="left: -5905px;">
                                <div class="switch_list zhankeng_first" id="zhankeng_first">
                                    <div class="img_wrap zhankeng_img1"><img src="https://pages.anjukestatic.com/usersite/site/img/global/defaultImg/big-deft-img.png" alt="暂无图片" height="450"></div>
                                    <div class="img_wrap zhankeng_img2"><img src="https://pages.anjukestatic.com/usersite/site/img/global/defaultImg/big-deft-img.png" alt="暂无图片" height="450"></div>
                                </div>
                                <div class="switch_list" id="room_pic_wrap">
                                    <div id="img_video" class="img_wrap img_video" data-trace="{exp_zfvideo:1}" data-visibled="true">
                                        <i id="video-player-icon" class="video-player"></i>
                                        <img src="https://pic1.ajkimg.com/display/anjuke/f2b0028f771683c52db0ca759d53fabc/600x600.jpg?t=1" alt="暂无图片" height="450">
                                        <div id="jp_container_1" class="video-wrap jp-video jp-video-270p" role="application" aria-label="media player" style="display: none;">
                                            <div class="jp-state-dialog jp-networkError-dialog">
                                                <div class="jp-state-bg"></div>
                                                <div class="jp-state-cont">
                                                    <p class="jp-state-text">您的网络状况不太稳定，请检查网络并刷新页面</p>
                                                    <div class="jp-button-bar">
                                                        <a class="jp-state-button jp-btn-reload" href="javascript:">刷新</a>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="jp-type-single">
                                                <div id="jquery_jplayer_1" class="jp-jplayer" style="width: 100%; height: 400px;"><img id="jp_poster_0" style="width: 100%; height: 400px; display: inline;"><video id="jp_video_0" preload="none" src="https://zjkzfvideo011.58wos.com.cn/mLNJiHgFeDmP/zftransform/14827a05-a9f8-461f-9568-da38b7107601.1748508955842.51544-3837671774.mp4" style="width: 0px; height: 0px;"></video></div>
                                                <div class="jp-gui">
                                                    <div class="jp-video-play" style="display: block;">
                                                        <a class="jp-video-play-icon" role="button" tabindex="0">play</a>
                                                    </div>
                                                    <div class="jp-interface">
                                                        <div class="jp-progress">
                                                            <div class="jp-seek-bar" style="width: 0%;">
                                                                <div class="jp-play-bar" style="width: 0%;"></div>
                                                            </div>
                                                        </div>
                                                        <div class="jp-controls-holder">
                                                            <div class="jp-controls">
                                                                <a class="jp-play jp-icon" role="button" tabindex="0">play</a>
                                                            </div>
                                                            <div class="jp-times"><span class="jp-current-time" role="timer" aria-label="time">00:00</span><span class="jp-all-time">/00:21</span></div>
                                                            <div class="jp-toggles">
                                                                <a title="循环播放" class="jp-repeat jp-icon" role="button" toggle="true" tabindex="0">repeat</a>
                                                                <a class="jp-full-screen jp-icon" role="button" tabindex="0">full screen</a>
                                                            </div>
                                                            <div class="jp-volume-controls">
                                                                <a class="jp-mute jp-icon" role="button" tabindex="0">mute</a>
                                                                <a class="jp-volume-max jp-icon" role="button" toggle="true" tabindex="0">max volume</a>
                                                                <div class="jp-volume-bar">
                                                                    <div class="jp-volume-bar-value" style="width: 80%;"></div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="jp-no-solution" style="display: none;">
                                                    <span>需要升级</span>您的浏览器版本过低，或需要安装新版flash<a href="http://get.adobe.com/flashplayer/" target="_blank">Flash plugin</a>.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="img_wrap">
                                        <img data-src="https://pic1.ajkimg.com/display/anjuke/f2b0028f771683c52db0ca759d53fabc/600x450c.jpg?t=1&amp;srotate=1" src="https://pic1.ajkimg.com/display/anjuke/f2b0028f771683c52db0ca759d53fabc/600x450c.jpg?t=1&amp;srotate=1" alt="长沙雨花洞井铺邓家湾安置房精装一室 汽车南站 德思勤商圈 邓家湾 押一付一 无需登记出租房源真实图片" height="450">
                                    </div>
                                    <div class="img_wrap">
                                        <img data-src="https://pic1.ajkimg.com/display/anjuke/74dceaf943677e69b4d5074199e5600e/600x450c.jpg?t=1&amp;srotate=1" src="https://pages.anjukestatic.com/usersite/site/img/global/defaultImg/big-load-img2.png" alt="长沙雨花洞井铺邓家湾安置房精装一室 汽车南站 德思勤商圈 邓家湾 押一付一 无需登记出租房源真实图片" height="450">
                                    </div>
                                    <div class="img_wrap">
                                        <img data-src="https://pic1.ajkimg.com/display/anjuke/8ea6c229d01d0bf42dd42bc22c196853/600x450c.jpg?t=1&amp;srotate=1" src="https://pages.anjukestatic.com/usersite/site/img/global/defaultImg/big-load-img2.png" alt="长沙雨花洞井铺邓家湾安置房精装一室 汽车南站 德思勤商圈 邓家湾 押一付一 无需登记出租房源真实图片" height="450">
                                    </div>
                                    <div class="img_wrap">
                                        <img data-src="https://pic1.ajkimg.com/display/anjuke/72f71da705a32f0a62918ea1b737c4f1/600x450c.jpg?t=1&amp;srotate=1" src="https://pages.anjukestatic.com/usersite/site/img/global/defaultImg/big-load-img2.png" alt="长沙雨花洞井铺邓家湾安置房精装一室 汽车南站 德思勤商圈 邓家湾 押一付一 无需登记出租房源真实图片" height="450">
                                    </div>
                                    <div class="img_wrap">
                                        <img data-src="https://pic1.ajkimg.com/display/anjuke/8a436b994e1de2238e545672eb369df9/600x450c.jpg?t=1&amp;srotate=1" src="https://pages.anjukestatic.com/usersite/site/img/global/defaultImg/big-load-img2.png" alt="长沙雨花洞井铺邓家湾安置房精装一室 汽车南站 德思勤商圈 邓家湾 押一付一 无需登记出租房源真实图片" height="450">
                                    </div>
                                    <div class="img_wrap">
                                        <img data-src="https://pic1.ajkimg.com/display/anjuke/78d36f02024d26c7f979834f59f8c1fb/600x450c.jpg?t=1&amp;srotate=1" src="https://pages.anjukestatic.com/usersite/site/img/global/defaultImg/big-load-img2.png" alt="长沙雨花洞井铺邓家湾安置房精装一室 汽车南站 德思勤商圈 邓家湾 押一付一 无需登记出租房源真实图片" height="450">
                                    </div>
                                </div>
                                <div class="switch_list" id="hx_pic_wrap">
                                    <div class="img_wrap">
                                        <img src="https://pages.anjukestatic.com/usersite/site/img/global/defaultImg/big-deft-img.png" alt="暂无图片" height="450">
                                    </div>
                                </div>
                                <div class="switch_list" id="surround_pic_wrap">
                                    <div class="img_wrap">
                                        <img src="https://pages.anjukestatic.com/usersite/site/img/global/defaultImg/big-deft-img.png" alt="暂无图片" height="450">
                                    </div>
                                </div>
                                <div class="switch_list zhankeng_last" id="zhankeng_last">
                                    <div class="img_wrap zhankeng_img1"><img src="https://pic1.ajkimg.com/display/anjuke/f2b0028f771683c52db0ca759d53fabc/600x600.jpg?t=1" alt="暂无图片" height="450"></div>
                                    <div class="img_wrap zhankeng_img2"><img data-src="https://pic1.ajkimg.com/display/anjuke/f2b0028f771683c52db0ca759d53fabc/600x450c.jpg?t=1&amp;srotate=1" src="https://pic1.ajkimg.com/display/anjuke/f2b0028f771683c52db0ca759d53fabc/600x450c.jpg?t=1&amp;srotate=1" alt="长沙雨花洞井铺邓家湾安置房精装一室 汽车南站 德思勤商圈 邓家湾 押一付一 无需登记出租房源真实图片" height="450"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="switch_tab_wrap" id="switch_tab_wrap">
                        <a class="switch_tag" href="javascript:;" data-track="pc_fydy_switch_snt" id="room_tab">
                            <i class="switch-icon iconfont switch-room-icon"></i>
                            <em class="title">室内图(6)</em>
                        </a>
                        <a class="switch_tag" href="javascript:;" data-track="pc_zfdy_xc_huxing_click" id="hx_tab">
                            <i class="switch-icon iconfont switch-hx-icon"></i>
                            <em class="title">户型图<span class="num">(0)</span></em>
                        </a>
                        <a class="switch_tag switch_tag_now" href="javascript:;" data-track="pc_zfdy_xc_hjt_click" id="surround_tab">
                            <i class="switch-icon iconfont switch-surd-icon"></i>
                            <em class="title">环境图<span class="num">(0)</span></em>
                        </a>
                        <a class="switch_tag map_tag_XL" href="javascript:;" data-track="pc_zfdy_xc_zbdt_click" id="map_tab" style="height: 107.8px;">
                            <i class="switch-icon iconfont"></i>
                            <em class="title">周边地图</em>
                        </a>
                    </div>
                </div>

                <div class="mod-title bottomed">
                    <h2 id="houseInfo" class="title nav-scroll">房屋信息</h2>
                    <div class="right-info">
                        <span class="code-num cd-official-check">
                            房源核验码：<a href="https://app.cszjxx.net:18080/house-supervision/verify/showHouseDetail/13832826403570530" target="_blank"><i style="color: #587eac;">13832826403570530</i></a>，
                            <span id="houseCode">房屋编码：4081555902163980，</span>更新时间：<b class="strongbox" style="font-weight: normal;">2025年05月29日</b>
                        </span>
                    </div>
                </div>
                <ul class="house-info-zufang cf">
                    <li class="full-line cf">
                        <span class="price"><em><b class="strongbox" style="font-weight: normal;">500</b></em>元/月</span>
                        <span class="type">付1押1</span>
                    </li>
                    <li class="house-info-item">
                        <span class="type">户型：</span>
                        <span class="info"><b class="strongbox" style="font-weight: normal;">1</b>室<b class="strongbox" style="font-weight: normal;">1</b>厅<b class="strongbox" style="font-weight: normal;">1</b>卫</span>
                    </li>
                    <li class="house-info-item">
                        <span class="type">面积：</span>
                        <span class="info"><b class="strongbox" style="font-weight: normal;">10.00平方米</b></span>
                    </li>
                    <li class="house-info-item">
                        <span class="type">朝向：</span>
                        <span class="info">朝南</span>
                    </li>
                    <li class="house-info-item">
                        <span class="type">楼层：</span>
                        <span class="info">中层(共7层)</span>
                    </li>
                    <li class="house-info-item">
                        <span class="type">装修：</span>
                        <span class="info">精装修</span>
                    </li>
                    <li class="house-info-item">
                        <span class="type">类型：</span>
                        <span class="info">普通住宅</span>
                    </li>
                    <li class="house-info-item">
                        <span class="type">小区：</span>
                        <a href="https://cs.anjuke.com/community/view/1418480" class="link" target="_blank" _soj="propview">邓家湾安置房</a>
                        &nbsp;(<a href="https://cs.zu.anjuke.com/fangyuan/yuhuah/" class="link" target="_blank" _soj="propview">雨花</a> <a href="https://cs.zu.anjuke.com/fangyuan/yuhuah-q-dongjingpu/" class="link" target="_blank" _soj="propview">洞井铺</a>)
                    </li>
                </ul>

                <div class="mod-title bottomed">
                    <h2 class="title">房屋配套</h2>
                </div>
                <ul class="house-info-peitao cf" data-trace="{'pc_zfdy_fypt_show':1}" data-visibled="true">
                    <li class="peitao-item has">
                        <svg class="icon-v2" aria-hidden="true"><use xlink:href="#icon-Bing-Xiang-Bei-Fen"></use></svg>
                        <div class="peitao-info">冰箱</div>
                    </li>
                    <li class="peitao-item has">
                        <svg class="icon-v2" aria-hidden="true"><use xlink:href="#icon-Xi-Yi-Ji-Bei-Fen"></use></svg>
                        <div class="peitao-info">洗衣机</div>
                    </li>
                    <li class="peitao-item has">
                        <svg class="icon-v2" aria-hidden="true"><use xlink:href="#icon-Re-Shui-Qi-Bei-Fen"></use></svg>
                        <div class="peitao-info">热水器</div>
                    </li>
                    <li class="peitao-item has">
                        <svg class="icon-v2" aria-hidden="true"><use xlink:href="#icon-wifiBei-Fen"></use></svg>
                        <div class="peitao-info">宽带</div>
                    </li>
                    <li class="peitao-item has">
                        <svg class="icon-v2" aria-hidden="true"><use xlink:href="#icon-Sha-Fa-Bei-Fen"></use></svg>
                        <div class="peitao-info">沙发</div>
                    </li>
                    <li class="peitao-item has">
                        <svg class="icon-v2" aria-hidden="true"><use xlink:href="#icon-You-Yan-Ji-Bei-Fen"></use></svg>
                        <div class="peitao-info">油烟机</div>
                    </li>
                    <li class="peitao-item ">
                        <svg class="icon-v2" aria-hidden="true"><use xlink:href="#icon-Ran-Qi-Zao-Wu"></use></svg>
                        <div class="peitao-info">燃气灶</div>
                    </li>
                    <li class="peitao-item has">
                        <svg class="icon-v2" aria-hidden="true"><use xlink:href="#icon-Ke-Zuo-Fan"></use></svg>
                        <div class="peitao-info">可做饭</div>
                    </li>
                    <li class="peitao-item ">
                        <svg class="icon-v2" aria-hidden="true"><use xlink:href="#icon-Dian-Shi-Bei-Fen-5"></use></svg>
                        <div class="peitao-info">电视</div>
                    </li>
                    <li class="peitao-item has">
                        <svg class="icon-v2" aria-hidden="true"><use xlink:href="#icon-Kong-Diao-Bei-Fen"></use></svg>
                        <div class="peitao-info">空调</div>
                    </li>
                    <li class="peitao-item has">
                        <svg class="icon-v2" aria-hidden="true"><use xlink:href="#icon-Yi-Ju-Bei-Fen"></use></svg>
                        <div class="peitao-info">衣柜</div>
                    </li>
                    <li class="peitao-item has">
                        <svg class="icon-v2" aria-hidden="true"><use xlink:href="#icon-Chuang-Bei-Fen"></use></svg>
                        <div class="peitao-info">床</div>
                    </li>
                    <li class="peitao-item has">
                        <svg class="icon-v2" aria-hidden="true"><use xlink:href="#icon-Wei-Sheng-Jian-Bei-Fen"></use></svg>
                        <div class="peitao-info">卫生间</div>
                    </li>
                    <li class="peitao-item has">
                        <svg class="icon-v2" aria-hidden="true"><use xlink:href="#icon-Zhi-Neng-Men-Suo-Bei-Fen"></use></svg>
                        <div class="peitao-info">智能门锁</div>
                    </li>
                    <li class="peitao-item has">
                        <svg class="icon-v2" aria-hidden="true"><use xlink:href="#icon-Yang-Tai-Bei-Fen"></use></svg>
                        <div class="peitao-info">阳台</div>
                    </li>
                    <li class="peitao-item has">
                        <svg class="icon-v2" aria-hidden="true"><use xlink:href="#icon-Nuan-Qi-Bei-Fen"></use></svg>
                        <div class="peitao-info">暖气</div>
                    </li>
                </ul>
                <div class="houseInfo-tags clearfix" id="proLinks">
                </div>

                <div class="mod-title bottomed">
                    <h2 class="title">房源概况</h2>
                </div>
                <div class="auto-general">
                    <b class="strongbox" style="font-weight: normal;">精装单间，押一付一，拎包入住，无需登记无288跟<br>
                    388看房废跟信息废直接看房价格500-700，房源多<br>
                    全新精装，家具家电齐全，租客年轻，管理严格，服务到位，小区环境优美，物业治安管理完善<br>
                    所有家具家电自然损坏维修全免，有公司人员对房间公共区深度保洁<br>
                    周边配套齐全，银行，医院，餐饮，娱乐，菜市场，商场，超市等设施齐全<br>
                    公交地铁交通便利，直接联系我随时看房<br>
                    根据你的预算和要求匹配合适的房源，绝不浪费您宝贵的时间</b>
                </div>

                <div class="zhujianwei-code">
                </div>

                <div id="php_data" style="display: none;" recomm="{&quot;prop_id&quot;:7149215998,&quot;prop_type&quot;:3,&quot;area_id&quot;:419,&quot;price&quot;:&quot;500&quot;,&quot;num&quot;:9,&quot;guid&quot;:&quot;041830A9-C02D-9015-1744-8F638ABE34CE&quot;}" iszhutui="0" recomnear="{&quot;cityid&quot;:27,&quot;id&quot;:7149215998,&quot;type&quot;:3,&quot;user_id&quot;:203588198,&quot;limit&quot;:9,&quot;resulttype&quot;:2,&quot;from&quot;:&quot;SITE_RENT_PAGE&quot;,&quot;r&quot;:453235940}" commname="邓家湾安置房" brokername="吴章元" rentbuy="{&quot;prop_id&quot;:7149215998,&quot;prop_type&quot;:3,&quot;area_id&quot;:419,&quot;price&quot;:&quot;500&quot;,&quot;num&quot;:4,&quot;guid&quot;:&quot;041830A9-C02D-9015-1744-8F638ABE34CE&quot;}">
                </div>
                <div class="correlation-broker-wrap" data-tracker-show="pc_detail_secondarycontact_show"></div>

                <div class="mod-title recommendGuess no-title">
                    <h2 class="title">猜你喜欢</h2>
                </div>
                <div id="recommendGuess" class="likebox" data-trace="{'pc_zfdy_cnxh_show':1}" data-visibled="true">
                    <div class="ajax_prop">
                        <ul class="cf box">
                            <li class="rec_common_con " clicklog="click_cainixihuan_new_detail" ep-log="{&quot;cityid1_58&quot;:414,&quot;cityid2_58&quot;:&quot;8015&quot;,&quot;cityid3_58&quot;:null,&quot;cate1&quot;:1,&quot;cate2&quot;:&quot;8&quot;,&quot;cate&quot;:&quot;1,211&quot;,&quot;infoid&quot;:4057634817187846,&quot;houseid&quot;:4057634817187846,&quot;is_biz&quot;:true,&quot;sid&quot;:&quot;10bc3ff31a25d8de9019a5e338fcd613&quot;,&quot;shidiheyanzhuangtai&quot;:&quot;1&quot;,&quot;qingheyanzhuangtai&quot;:&quot;&quot;,&quot;nameoflist&quot;:14,&quot;recomshowlog&quot;:&quot;recomlog:scene=pc_detail_hezu_nearby&amp;seqno=CD0C0BF6B5801701EE7AC06654FE3169&amp;recallno=0&amp;rankno=0&amp;ruleno=0&amp;viewno=0&amp;recalldetail=1&amp;pos=1&amp;infoid=4057634817187846&amp;curinfoid=0&amp;localid=414&amp;appname=58app&amp;isBiz=1&amp;skuabtest=d&amp;d1=d&amp;devicetype=pc&amp;curhouseid=0&amp;intervalnum=4&amp;apptype=anjuke&amp;miniappresource=&amp;houseid=4057634817187846&amp;ssp=0&amp;type=2&quot;,&quot;GTID&quot;:&quot;10bc3ff31a25d8de9019a5e338fcd613&quot;,&quot;ad_type&quot;:&quot;p&quot;,&quot;tid&quot;:&quot;&quot;,&quot;is_down&quot;:0,&quot;is_up&quot;:&quot;4&quot;,&quot;slot&quot;:&quot;ajk_rent_pc_detail_bottom&quot;,&quot;infotype&quot;:9}" judge_visible="" fortrack-data="exposure_cainixihuan_new_detail" data-visibled="true">
                                <img class="rec_common_img" title="农大 龙湖天街地铁六号线旭日嘉苑精装两房楼下100米就是地铁" width="150" height="115" lazy_src="http://pic8.58cdn.com.cn/anjuke_58/7e71bcb404197b90ecaee52e947b77b5?w=240&amp;h=180&amp;crop=1&amp;t=1&amp;srotate=1&amp;srotate=1" src="http://pic8.58cdn.com.cn/anjuke_58/7e71bcb404197b90ecaee52e947b77b5?w=240&amp;h=180&amp;crop=1&amp;t=1&amp;srotate=1&amp;srotate=1" data-loaded="true">
                                <i class="v-small"></i>
                                <a class="rec_common_title" target="_blank" data-sign="true" data-trace="{viewandview_1:4057634817187846}" title="农大 龙湖天街地铁六号线旭日嘉苑精装两房楼下100米就是地铁" href="https://cs.zu.anjuke.com/fangyuan/4057634817187846?isauction=feedcommonhouse&amp;shangquan_id=&amp;from=zufang_page_rec_cnxh&amp;cityId58=414&amp;localName=cs&amp;pagesource=14" pos="0" data-visibled="true">
                                    农大 龙湖天街地铁六号线旭日嘉苑精装两房楼下100米就是地铁
                                </a>
                                <p class="rec_common_price">1300<span>元/月</span></p>
                                <p class="rec_common_info">2室2厅，50.0平米</p>
                                <p class="rec_common_name">旭日嘉苑</p>
                            </li>
                            <li class="rec_common_con " clicklog="click_cainixihuan_new_detail" ep-log="{&quot;cityid1_58&quot;:414,&quot;cityid2_58&quot;:&quot;8115&quot;,&quot;cityid3_58&quot;:null,&quot;cate1&quot;:1,&quot;cate2&quot;:&quot;8&quot;,&quot;cate&quot;:&quot;1,211&quot;,&quot;infoid&quot;:4040602521590786,&quot;houseid&quot;:4040602521590786,&quot;is_biz&quot;:true,&quot;sid&quot;:&quot;10bc3ff31a25d8de9019a5e338fcd613&quot;,&quot;shidiheyanzhuangtai&quot;:&quot;1&quot;,&quot;qingheyanzhuangtai&quot;:&quot;&quot;,&quot;nameoflist&quot;:14,&quot;recomshowlog&quot;:&quot;recomlog:scene=pc_detail_hezu_nearby&amp;seqno=CD0C0BF6B5801701EE7AC06654FE3169&amp;recallno=0&amp;rankno=0&amp;ruleno=0&amp;viewno=0&amp;recalldetail=1&amp;pos=2&amp;infoid=4040602521590786&amp;curinfoid=0&amp;localid=414&amp;appname=58app&amp;isBiz=1&amp;skuabtest=d&amp;d1=d&amp;devicetype=pc&amp;curhouseid=0&amp;intervalnum=4&amp;apptype=anjuke&amp;miniappresource=&amp;houseid=4040602521590786&amp;ssp=0&amp;type=2&quot;,&quot;GTID&quot;:&quot;10bc3ff31a25d8de9019a5e338fcd613&quot;,&quot;ad_type&quot;:&quot;p&quot;,&quot;tid&quot;:&quot;&quot;,&quot;is_down&quot;:0,&quot;is_up&quot;:&quot;4&quot;,&quot;slot&quot;:&quot;ajk_rent_pc_detail_bottom&quot;,&quot;infotype&quot;:9}" judge_visible="" fortrack-data="exposure_cainixihuan_new_detail" data-visibled="true">
                                <img class="rec_common_img" title="网红奶油风一房 近黄土岭 第二医院 网红居住地 可短租月付" width="150" height="115" lazy_src="http://pic1.58cdn.com.cn/anjuke_58/ca98cdef4237a8bfa198d4e603b5d1ef?w=240&amp;h=180&amp;crop=1&amp;t=1&amp;srotate=1&amp;srotate=1" src="http://pic1.58cdn.com.cn/anjuke_58/ca98cdef4237a8bfa198d4e603b5d1ef?w=240&amp;h=180&amp;crop=1&amp;t=1&amp;srotate=1&amp;srotate=1" data-loaded="true">
                                <i class="v-small"></i>
                                <a class="rec_common_title" target="_blank" data-sign="true" data-trace="{viewandview_1:4040602521590786}" title="网红奶油风一房 近黄土岭 第二医院 网红居住地 可短租月付" href="https://cs.zu.anjuke.com/fangyuan/4040602521590786?isauction=feedcommonhouse&amp;shangquan_id=&amp;from=zufang_page_rec_cnxh&amp;cityId58=414&amp;localName=cs&amp;pagesource=14" pos="1" data-visibled="true">
                                    网红奶油风一房 近黄土岭 第二医院 网红居住地 可短租月付
                                </a>
                                <p class="rec_common_price">1350<span>元/月</span></p>
                                <p class="rec_common_info">1室1厅，60平米</p>
                                <p class="rec_common_name">保险公司宿舍(南湖路金盆岭)</p>
                            </li>
                            <li class="rec_common_con " clicklog="click_cainixihuan_new_detail" ep-log="{&quot;cityid1_58&quot;:414,&quot;cityid2_58&quot;:&quot;8054&quot;,&quot;cityid3_58&quot;:null,&quot;cate1&quot;:1,&quot;cate2&quot;:&quot;8&quot;,&quot;cate&quot;:&quot;1,211&quot;,&quot;infoid&quot;:4074564562627597,&quot;houseid&quot;:4074564562627597,&quot;is_biz&quot;:true,&quot;sid&quot;:&quot;10bc3ff31a25d8de9019a5e338fcd613&quot;,&quot;shidiheyanzhuangtai&quot;:&quot;1&quot;,&quot;qingheyanzhuangtai&quot;:&quot;&quot;,&quot;nameoflist&quot;:14,&quot;recomshowlog&quot;:&quot;recomlog:scene=pc_detail_hezu_nearby&amp;seqno=CD0C0BF6B5801701EE7AC06654FE3169&amp;recallno=0&amp;rankno=0&amp;ruleno=0&amp;viewno=0&amp;recalldetail=1&amp;pos=3&amp;infoid=4074564562627597&amp;curinfoid=0&amp;localid=414&amp;appname=58app&amp;isBiz=1&amp;skuabtest=d&amp;d1=d&amp;devicetype=pc&amp;curhouseid=0&amp;intervalnum=4&amp;apptype=anjuke&amp;miniappresource=&amp;houseid=4074564562627597&amp;ssp=0&amp;type=2&quot;,&quot;GTID&quot;:&quot;10bc3ff31a25d8de9019a5e338fcd613&quot;,&quot;ad_type&quot;:&quot;p&quot;,&quot;tid&quot;:&quot;&quot;,&quot;is_down&quot;:0,&quot;is_up&quot;:&quot;6&quot;,&quot;slot&quot;:&quot;ajk_rent_pc_detail_bottom&quot;,&quot;infotype&quot;:9}" judge_visible="" fortrack-data="exposure_cainixihuan_new_detail" data-visibled="true">
                                <img class="rec_common_img" title="芙蓉区 近华天大酒店 湖南大剧院 袁家岭地铁口 无中介" width="150" height="115" lazy_src="http://pic3.58cdn.com.cn/anjuke_58/eae3011ab5829ba427a268baf1d61ded?w=240&amp;h=180&amp;crop=1&amp;t=1&amp;srotate=1&amp;srotate=1" src="http://pic3.58cdn.com.cn/anjuke_58/eae3011ab5829ba427a268baf1d61ded?w=240&amp;h=180&amp;crop=1&amp;t=1&amp;srotate=1&amp;srotate=1" data-loaded="true">
                                <a class="rec_common_title" target="_blank" data-sign="true" data-trace="{viewandview_1:4074564562627597}" title="芙蓉区 近华天大酒店 湖南大剧院 袁家岭地铁口 无中介" href="https://cs.zu.anjuke.com/fangyuan/4074564562627597?isauction=feedcommonhouse&amp;shangquan_id=&amp;from=zufang_page_rec_cnxh&amp;cityId58=414&amp;localName=cs&amp;pagesource=14" pos="2" data-visibled="true">
                                    芙蓉区 近华天大酒店 湖南大剧院 袁家岭地铁口 无中介
                                </a>
                                <p class="rec_common_price">700<span>元/月</span></p>
                                <p class="rec_common_info">1室1厅，50平米</p>
                                <p class="rec_common_name">二里牌集体公寓</p>
                            </li>
                            <li class="rec_common_con " clicklog="click_cainixihuan_new_detail" ep-log="{&quot;cityid1_58&quot;:414,&quot;cityid2_58&quot;:&quot;8016&quot;,&quot;cityid3_58&quot;:null,&quot;cate1&quot;:1,&quot;cate2&quot;:&quot;8&quot;,&quot;cate&quot;:&quot;1,211&quot;,&quot;infoid&quot;:4066463391919107,&quot;houseid&quot;:4066463391919107,&quot;is_biz&quot;:true,&quot;sid&quot;:&quot;10bc3ff31a25d8de9019a5e338fcd613&quot;,&quot;shidiheyanzhuangtai&quot;:&quot;1&quot;,&quot;qingheyanzhuangtai&quot;:&quot;&quot;,&quot;nameoflist&quot;:14,&quot;recomshowlog&quot;:&quot;recomlog:scene=pc_detail_hezu_nearby&amp;seqno=CD0C0BF6B5801701EE7AC06654FE3169&amp;recallno=0&amp;rankno=0&amp;ruleno=0&amp;viewno=0&amp;recalldetail=1&amp;pos=4&amp;infoid=4066463391919107&amp;curinfoid=0&amp;localid=414&amp;appname=58app&amp;isBiz=1&amp;skuabtest=d&amp;d1=d&amp;devicetype=pc&amp;curhouseid=0&amp;intervalnum=4&amp;apptype=anjuke&amp;miniappresource=&amp;houseid=4066463391919107&amp;ssp=0&amp;type=2&quot;,&quot;GTID&quot;:&quot;10bc3ff31a25d8de9019a5e338fcd613&quot;,&quot;ad_type&quot;:&quot;p&quot;,&quot;tid&quot;:&quot;&quot;,&quot;is_down&quot;:0,&quot;is_up&quot;:&quot;4&quot;,&quot;slot&quot;:&quot;ajk_rent_pc_detail_bottom&quot;,&quot;infotype&quot;:9}" judge_visible="" fortrack-data="exposure_cainixihuan_new_detail" data-visibled="true">
                                <img class="rec_common_img" title="押一付一无中介費2号线地铁口万家丽高桥芙蓉区政府火车站" width="150" height="115" lazy_src="http://pic5.58cdn.com.cn/anjuke_58/b870ef5386af6324b64bd2e3cbafe876?w=240&amp;h=180&amp;crop=1&amp;t=1&amp;srotate=1&amp;srotate=1" src="http://pic5.58cdn.com.cn/anjuke_58/b870ef5386af6324b64bd2e3cbafe876?w=240&amp;h=180&amp;crop=1&amp;t=1&amp;srotate=1&amp;srotate=1" data-loaded="true">
                                <a class="rec_common_title" target="_blank" data-sign="true" data-trace="{viewandview_1:4066463391919107}" title="押一付一无中介費2号线地铁口万家丽高桥芙蓉区政府火车站" href="https://cs.zu.anjuke.com/fangyuan/4066463391919107?isauction=feedcommonhouse&amp;shangquan_id=&amp;from=zufang_page_rec_cnxh&amp;cityId58=414&amp;localName=cs&amp;pagesource=14" pos="3" data-visibled="true">
                                    押一付一无中介費2号线地铁口万家丽高桥芙蓉区政府火车站
                                </a>
                                <p class="rec_common_price">2300<span>元/月</span></p>
                                <p class="rec_common_info">2室2厅，90.92平米</p>
                                <p class="rec_common_name">东玺门</p>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="mod-title">
                    <h2 id="commArround" class="title nav-scroll" data-trace="{'pc_zfdy_dt_show':1}" data-visibled="true">邓家湾安置房</h2>
                    <a href="https://cs.anjuke.com/community/view/1418480" class="mod-title-link" target="_blank" _soj="pc_zfdy_xq">查看小区详情</a>
                </div>
                <div class="comm-commoninfo">
                    <div class="mapCon">
                        <div class="traffic_map_wrap" id="traffic_map_wrap">
                            <div class="scale_btn_wrap">
                                <a href="javascript:void(0);" class="zoom_btn" id="zoomout_btn">+</a>
                                <a href="javascript:void(0);" class="zoom_btn minus-btn" id="zoomin_btn">-</a>
                            </div>
                            <div class="bmap_wrap">
                                <div class="bmap" id="bmap" style="overflow: hidden; position: relative; z-index: 0; background-color: rgb(243, 241, 236); color: rgb(0, 0, 0); text-align: left;"><div style="overflow: visible; position: absolute; z-index: 0; left: 0px; top: 0px; cursor: url(&quot;https://api.map.baidu.com/images/openhand.cur&quot;) 8 8, default;"><div class="BMap_mask" style="position: absolute; left: 0px; top: 0px; z-index: 9; overflow: hidden; user-select: none; width: 900px; height: 360px;"></div><div style="position: absolute; height: 0px; width: 0px; left: 0px; top: 0px; z-index: 200;"><div style="position: absolute; height: 0px; width: 0px; left: 0px; top: 0px; z-index: 800;"></div><div style="position: absolute; height: 0px; width: 0px; left: 0px; top: 0px; z-index: 700;"></div><div style="position: absolute; height: 0px; width: 0px; left: 0px; top: 0px; z-index: 600;"></div><div style="position: absolute; height: 0px; width: 0px; left: 0px; top: 0px; z-index: 500;"><div class="commonMapC" id="common" style="user-select: none; left: 435px; top: 122px;"><p class="common_name">邓家湾安置房</p><i class="common_arrow_ico"></i><i class="common_pos_ico"></i></div><label class="BMapLabel" unselectable="on" style="position: absolute; display: none; cursor: inherit; background-color: rgb(190, 190, 190); border: 1px solid rgb(190, 190, 190); padding: 1px; white-space: nowrap; font: 12px arial, sans-serif; z-index: -20000; color: rgb(190, 190, 190);">shadow</label></div><div style="position: absolute; height: 0px; width: 0px; left: 0px; top: 0px; z-index: 400;"></div><div style="position: absolute; height: 0px; width: 0px; left: 0px; top: 0px; z-index: 300;"></div><div style="position: absolute; height: 0px; width: 0px; left: 0px; top: 0px; z-index: 201;"></div><div style="position: absolute; height: 0px; width: 0px; left: 0px; top: 0px; z-index: 200;"><svg version="1.1" type="system" x="1900px" y="1360px" viewBox="-500 -500 1900 1360" style="position: absolute; top: -500px; left: -500px; width: 1900px; height: 1360px;"><path stroke-linejoin="round" stroke-linecap="round" fill-rule="evenodd" stroke="#CCC297" stroke-width="1" stroke-dasharray="none" fill="#CCC297" stroke-opacity="0.5" fill-opacity="0.3" style="cursor: pointer; user-select: none;" d="M 450 39 L 428 41 406 46 386 54 367 66 350 80 335 97 324 116 315 136 310 158 308 180 310 202 315 224 324 244 335 263 350 280 367 294 386 306 406 314 428 319 450 321 472 319 494 314 514 306 533 294 550 280 565 263 576 244 585 224 590 202 592 180 590 158 585 136 576 116 565 97 550 80 533 66 514 54 494"></path></svg></div></div></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            </div>
    </div>
</div>
</template>

<script setup>

</script>
<style>
 @font-face {
            font-family: 'iconfont'; /* project id 888888 */
            src: url('//at.alicdn.com/t/c/font_888888_example.woff2') format('woff2'),
                 url('//at.alicdn.com/t/c/font_888888_example.woff') format('woff'),
                 url('//at.alicdn.com/t/c/font_888888_example.ttf') format('truetype');
            /* 请替换为你的 iconfont 地址 */
        }

        .iconfont {
            font-family: "iconfont" !important;
            font-size: 16px;
            font-style: normal;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 假设图标的 Unicode 编码 */
        .switch-room-icon::before { content: "\e60e"; } /* 室内图图标示例 */
        .switch-hx-icon::before { content: "\e60f"; } /* 户型图图标示例 */
        .switch-surd-icon::before { content: "\e610"; } /* 环境图图标示例 */
        .map_tag_XL .iconfont::before { content: "\e611"; } /* 地图图标示例 */
        /* 通用重置和基础样式 */
* {
    box-sizing: border-box; /* 盒模型为边框盒，包含内边距和边框 */
    margin: 0;
    padding: 0;
}

body {
    font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif; /* 常用中文字体优先 */
    color: #333; /* 主体文字颜色 */
    font-size: 14px; /* 基础字号 */
    line-height: 1.6; /* 行高 */
    background-color: #f5f5f5; /* 页面背景色，通常是浅灰色 */
}

/* 清除浮动 */
.clearfix::after,
.cf::after { /* 兼容两种写法 */
    content: "";
    display: table;
    clear: both;
}

.wrapper {
    width: 1200px; /* 页面主体宽度，可根据设计图调整 */
    margin: 20px auto; /* 居中显示，上下外边距20px */
    background-color: #fff; /* 背景色为白色 */
    padding: 20px; /* 内边距 */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); /* 简单阴影效果 */
    border-radius: 4px; /* 边角圆润 */
}

/* 房屋标题部分 */
.house-title {
    font-size: 24px; /* 标题字号 */
    font-weight: bold; /* 标题加粗 */
    margin-bottom: 15px;
    display: flex; /* 使用 Flexbox 布局 */
    align-items: center; /* 垂直居中对齐 */
    flex-wrap: wrap; /* 允许标签在空间不足时换行 */
    line-height: 1.3;
}

.tit-rest {
    flex-grow: 1; /* 占据剩余空间 */
    margin-right: 10px;
    color: #333;
}

.house-title-tag-box {
    display: flex;
    align-items: center;
    flex-shrink: 0; /* 不收缩 */
}

.anxvan-ico {
    background-color: #4CAF50; /* “安选”标签背景色 */
    color: #fff; /* 文字颜色 */
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    font-style: normal; /* 移除斜体 */
    margin-right: 5px;
    white-space: nowrap; /* 防止文字换行 */
}

.dzrq-icon {
    width: 20px; /* 图标宽度 */
    height: 20px; /* 图标高度 */
    vertical-align: middle; /* 垂直对齐 */
}

/* 基础信息部分 */
.title-basic-info {
    margin-bottom: 20px;
    border-bottom: 1px dashed #eee; /* 底部虚线 */
    padding-bottom: 15px;
    position: relative; /* 为举报按钮做定位参考 */
}

.info-tag {
    display: inline-block; /* 行内块元素 */
    margin-right: 20px;
    font-size: 16px;
    color: #666;
    vertical-align: middle; /* 垂直对齐 */
}

.info-tag.light {
    color: #FF6600; /* 价格高亮颜色 */
    font-size: 20px; /* 价格字号 */
    font-weight: bold;
}

.info-tag em {
    font-style: normal; /* 移除斜体 */
}

.info-tag b {
    font-weight: normal; /* 覆盖 HTML 中的行内样式 */
}

.title-label {
    list-style: none; /* 移除列表点 */
    display: inline-block;
    margin-left: 10px;
    vertical-align: middle;
}

.title-label-item {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    margin-right: 5px;
    white-space: nowrap;
}

.title-label-item.rent {
    background-color: #e0f2f7; /* “合租”标签背景色 */
    color: #337ab7; /* “合租”标签文字颜色 */
}

.title-label-item.buy {
    background-color: #fffde7; /* “朝南”标签背景色 */
    color: #fbc02d; /* “朝南”标签文字颜色 */
}

.title-label-item.subway {
    /* 地铁标签样式，如果存在 */
}

.report-btn {
    position: absolute;
    right: 0;
    top: 0;
    display: flex;
    align-items: center;
    text-decoration: none; /* 移除下划线 */
    color: #999;
    font-size: 12px;
    cursor: pointer;
}

.report-btn:hover {
    color: #FF6600;
}

.report-btn-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    background-image: url('path/to/report-icon.png'); /* 请替换为实际图标路径 */
    background-size: contain;
    background-repeat: no-repeat;
    margin-right: 3px;
}

.report-phone {
    position: absolute;
    right: 0;
    top: 25px; /* 调整位置 */
    font-size: 12px;
    color: #999;
    text-align: right;
    line-height: 1.2;
}

/* 主内容区域 */
.mainbox {
    display: flex; /* Flexbox 布局 */
    justify-content: space-between; /* 左右两端对齐 */
    margin-top: 20px;
}

.lbox {
    flex: 1; /* 占据剩余空间 */
    margin-right: 20px; /* 与右侧边栏的间距，如果右侧边栏不存在则可忽略 */
    min-width: 0; /* 允许内容缩小，防止溢出 */
}

/* 图片/视频切换器 */
.switch_warpper {
    margin-bottom: 20px;
    position: relative;
    border: 1px solid #eee;
    background-color: #fcfcfc;
}

.switch_with_map {
    position: relative;
    overflow: hidden; /* 隐藏超出部分 */
    height: 450px; /* 固定高度 */
}

.inner_switcher {
    position: relative;
    height: 100%;
}

.switch_mask {
    position: absolute;
    top: 0;
    width: 80px; /* 遮罩宽度 */
    height: 100%;
    background: linear-gradient(to right, rgba(0,0,0,0.5), rgba(0,0,0,0)); /* 渐变背景 */
    z-index: 10;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #fff;
    cursor: pointer;
    opacity: 0; /* 默认隐藏，通过JS或hover显示 */
    transition: opacity 0.3s ease; /* 过渡效果 */
    text-shadow: 0 0 5px rgba(0,0,0,0.5);
}

.switch_mask:hover {
    opacity: 1;
}

.switch_left_mask {
    left: 0;
    background: linear-gradient(to right, rgba(0,0,0,0.5), rgba(0,0,0,0));
}

.switch_right_mask {
    right: 0;
    background: linear-gradient(to left, rgba(0,0,0,0.5), rgba(0,0,0,0));
}

.switch_btn {
    font-size: 30px;
}

.switch_mask span {
    font-size: 14px;
    margin-top: 5px;
}

.switch_wrap {
    display: flex;
    height: 100%;
    position: absolute; /* 由JavaScript控制 left 属性实现滑动效果 */
    left: 0;
    transition: left 0.5s ease-out; /* 滑动动画 */
}

.switch_list {
    display: flex;
    flex-shrink: 0; /* 防止列表项收缩 */
    height: 100%;
    /* 实际宽度由内部图片决定或通过JS设置 */
}

.img_wrap {
    flex-shrink: 0; /* 防止图片容器收缩 */
    width: 600px; /* 根据HTML中的图片宽度设置 */
    height: 450px; /* 固定高度 */
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f0f0f0; /* 占位符背景色 */
    overflow: hidden; /* 确保图片不溢出 */
}

.img_wrap img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain; /* 保持图片比例，适应容器 */
    display: block; /* 移除图片底部空白 */
}

.img_video {
    position: relative;
    cursor: pointer;
}

.video-player {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%); /* 居中 */
    font-size: 60px; /* 播放图标大小 */
    color: rgba(255, 255, 255, 0.8); /* 播放图标颜色 */
    /* 可能需要在这里放置实际的播放图标 Unicode 编码 */
}

/* 视频播放器样式（默认隐藏，通过JS控制显示） */
.video-wrap {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #000;
    display: none; /* 默认隐藏 */
    z-index: 1000; /* 确保在最上层 */
}
/* 这里是jPlayer的默认样式，通常不需要手动写太多，它会自己生成 */
/* .jp-video-play { ... } */
/* .jp-interface { ... } */

/* 切换器标签 */
.switch_tab_wrap {
    display: flex;
    justify-content: space-around;
    border-top: 1px solid #eee;
    background-color: #fcfcfc;
}

.switch_tag {
    flex: 1; /* 平均分配空间 */
    text-align: center;
    padding: 10px 0;
    text-decoration: none;
    color: #666;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-right: 1px solid #eee; /* 右侧边框 */
    transition: color 0.2s ease, border-bottom 0.2s ease;
}

.switch_tag:last-child {
    border-right: none; /* 最后一个无右边框 */
}

.switch_tag:hover {
    color: #FF6600; /* 鼠标悬停颜色 */
}

.switch_tag.switch_tag_now {
    color: #FF6600; /* 激活状态颜色 */
    border-bottom: 2px solid #FF6600; /* 激活状态底部边框 */
    padding-bottom: 8px; /* 调整内边距以适应底部边框 */
}

.switch-icon {
    font-size: 20px; /* 图标大小 */
    margin-bottom: 5px;
}

.switch_tag .title {
    font-size: 14px;
}

.switch_tag .num {
    font-size: 12px;
    margin-left: 3px;
    color: #999;
}

/* 各个信息模块标题 */
.mod-title {
    margin-top: 30px;
    margin-bottom: 15px;
    border-bottom: 1px solid #eee; /* 底部细线 */
    padding-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: flex-end; /* 标题和右侧信息底部对齐 */
}

.mod-title.bottomed {
    margin-top: 20px; /* 连续模块之间的间距 */
}

.mod-title .title {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    flex-grow: 1;
}

.mod-title .right-info {
    font-size: 12px;
    color: #999;
    flex-shrink: 0;
    text-align: right;
}

.mod-title .code-num {
    margin-left: 10px;
    white-space: nowrap; /* 防止换行 */
}

.mod-title .code-num a {
    color: #587eac; /* 链接颜色 */
    text-decoration: none;
}

.mod-title .code-num a:hover {
    text-decoration: underline;
}

.mod-title .mod-title-link {
    font-size: 14px;
    color: #FF6600; /* 链接颜色 */
    text-decoration: none;
    margin-left: 15px;
    white-space: nowrap;
}

.mod-title .mod-title-link:hover {
    text-decoration: underline;
}

/* 房屋信息列表 */
.house-info-zufang {
    list-style: none;
    display: flex; /* Flexbox 布局 */
    flex-wrap: wrap; /* 允许换行 */
    margin-bottom: 20px;
}

.house-info-zufang li {
    width: 33.33%; /* 每行三列 */
    padding: 10px 0;
    display: flex;
    align-items: baseline;
    line-height: 1.4;
}

.house-info-zufang li.full-line {
    width: 100%; /* 占满一行 */
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
    margin-bottom: 15px;
    align-items: center;
}

.house-info-zufang .type {
    color: #999;
    margin-right: 10px;
    flex-shrink: 0; /* 防止文字收缩 */
    width: 50px; /* 固定宽度，对齐效果更好 */
    text-align: right;
}

.house-info-zufang .info {
    color: #333;
    font-weight: normal;
    flex-grow: 1;
}

.house-info-zufang .price {
    font-size: 28px;
    color: #FF6600;
    font-weight: bold;
    margin-right: 20px;
    line-height: 1; /* 价格行高，防止过高 */
}

.house-info-zufang .price em {
    font-style: normal;
}

.house-info-zufang .type + .info { /* 特殊样式，如“付1押1” */
    font-size: 16px;
    color: #666;
}

.house-info-zufang .link {
    color: #587eac;
    text-decoration: none;
}

.house-info-zufang .link:hover {
    text-decoration: underline;
}

/* 房屋配套 */
.house-info-peitao {
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
    border: 1px solid #eee;
    border-bottom: none;
    background-color: #fcfcfc;
}

.peitao-item {
    width: 16.666%; /* 假设每行6个，如果图片显示是5个，则调整为20% */
    text-align: center;
    padding: 15px 0;
    color: #ccc; /* 默认未配备颜色 */
    border-bottom: 1px solid #eee;
    border-right: 1px solid #eee;
}
/* 每行的最后一个项目移除右边框 */
.peitao-item:nth-child(6n) {
    border-right: none;
}

/* 第一个项目移除左边框 */
.peitao-item:nth-child(6n + 1) {
    /* 假设它总是第一个 */
}


.peitao-item.has {
    color: #333; /* 已配备的颜色 */
}

/* SVG 图标的样式 */
.peitao-item svg.icon-v2 {
    width: 30px; /* 图标宽度 */
    height: 30px; /* 图标高度 */
    fill: currentColor; /* 填充颜色继承父元素文字颜色 */
    margin-bottom: 5px;
    display: block; /* 块级显示 */
    margin-left: auto;
    margin-right: auto;
}

.peitao-info {
    font-size: 14px;
}

/* 房源概况 */
.auto-general {
    background-color: #f8f8f8;
    border: 1px solid #eee;
    padding: 15px;
    margin-bottom: 30px;
    line-height: 1.8;
    color: #555;
    white-space: pre-wrap; /* 保留HTML中的换行符 */
    border-radius: 4px;
}

/* 猜你喜欢 */
.recommendGuess {
    margin-top: 30px;
    padding-top: 15px; /* 与上方内容拉开距离 */
}

.recommendGuess .title {
    margin-bottom: 15px;
    border-bottom: none; /* 移除标题下划线，因为整个盒子有下划线 */
}

.likebox {
    margin-bottom: 30px;
}

.ajax_prop ul {
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    gap: 20px; /* Flexbox 间距 */
    /* justify-content: space-between; 如果需要分散对齐 */
}

.ajax_prop li {
    width: calc(25% - 15px); /* 每行4个，减去间距，15px是 (3 * gap) / 4 */
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 10px;
    position: relative;
    border-radius: 4px;
    overflow: hidden; /* 隐藏超出部分，尤其是标题 */
    text-align: left;
    display: flex;
    flex-direction: column;
}

.rec_common_img {
    width: 100%;
    height: 115px; /* 固定高度 */
    object-fit: cover; /* 裁剪并填充容器 */
    margin-bottom: 10px;
    border-radius: 3px;
}

.v-small {
    /* 视频播放小图标样式，通常绝对定位在图片上 */
    position: absolute;
    top: 15px;
    right: 15px;
    width: 24px;
    height: 24px;
    background-color: rgba(0,0,0,0.6);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 12px;
    /* 假设内容是一个小的播放图标或文字 */
}

.rec_common_title {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    text-decoration: none;
    display: -webkit-box; /* 限制行数 */
    -webkit-line-clamp: 2; /* 最多显示2行 */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    min-height: 36px; /* 确保两行的高度，即使只有一行 */
    margin-bottom: 5px;
}

.rec_common_title:hover {
    color: #FF6600;
}

.rec_common_price {
    font-size: 18px;
    color: #FF6600;
    font-weight: bold;
    margin-top: auto; /* 将价格推到底部 */
    margin-bottom: 5px;
    white-space: nowrap;
}

.rec_common_price span {
    font-size: 12px;
    font-weight: normal;
    color: #666;
    margin-left: 3px;
}

.rec_common_info,
.rec_common_name {
    font-size: 12px;
    color: #999;
    margin-bottom: 3px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 地图部分 */
.comm-commoninfo {
    margin-bottom: 30px;
}

.mapCon {
    position: relative;
    height: 360px; /* 地图固定高度 */
    border: 1px solid #eee;
    margin-top: 15px;
    background-color: #f0f0f0; /* 地图加载前的背景色 */
}

.traffic_map_wrap {
    width: 100%;
    height: 100%;
}

.scale_btn_wrap {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 100; /* 确保按钮在地图上方 */
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 3px;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* 隐藏边框重叠 */
}

.zoom_btn {
    display: block;
    width: 30px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    font-size: 20px;
    color: #333;
    text-decoration: none;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    background-color: #fff;
}

.zoom_btn:last-child {
    border-bottom: none;
}

.zoom_btn:hover {
    background-color: #f0f0f0;
}

.bmap_wrap, .bmap {
    width: 100%;
    height: 100%;
    /* 百度地图的样式由其SDK加载后自动生成，这里只需设置容器大小 */
}

.commonMapC {
    position: absolute;
    /* 示例：地图上的小区名称标记 */
    background-color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    box-shadow: 0 1px 5px rgba(0,0,0,0.2);
    display: flex;
    align-items: center;
    white-space: nowrap;
    transform: translate(-50%, -100%); /* 向上居中，箭头在底部 */
    margin-bottom: 10px; /* 留出箭头空间 */
}
.commonMapC .common_name {
    font-size: 14px;
    color: #333;
    font-weight: bold;
    margin-right: 5px;
}
.commonMapC .common_arrow_ico {
    /* 模拟箭头 */
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid #fff;
    z-index: 1;
}
.commonMapC .common_pos_ico {
    /* 定位小图标 */
    display: inline-block;
    width: 16px;
    height: 16px;
    background-image: url('path/to/map-pin-icon.png'); /* 替换为地图标记图标 */
    background-size: contain;
    margin-right: 5px;
}


/* 响应式设计 */
@media (max-width: 992px) {
    .wrapper {
        width: 95%;
        padding: 15px;
    }

    .ajax_prop li {
        width: calc(33.33% - 15px); /* 每行3个 */
    }
}

@media (max-width: 768px) {
    .mainbox {
        flex-direction: column; /* 堆叠布局 */
    }

    .lbox {
        width: 100%;
        margin-right: 0;
    }

    .house-info-zufang li {
        width: 50%; /* 每行两列 */
    }

    .peitao-item {
        width: 25%; /* 每行4个 */
    }
    .peitao-item:nth-child(6n) { /* 移除6列的右边框规则 */
        border-right: 1px solid #eee;
    }
    .peitao-item:nth-child(4n) { /* 每行4个的右边框规则 */
        border-right: none;
    }

    .ajax_prop li {
        width: calc(50% - 10px); /* 每行2个 */
    }

    .house-title {
        font-size: 20px;
    }

    .info-tag.light {
        font-size: 18px;
    }

    .switch_wrap .img_wrap {
        width: 100%; /* 图片宽度适应父容器 */
    }
}

@media (max-width: 480px) {
    .wrapper {
        padding: 10px;
    }

    .house-title {
        flex-direction: column;
        align-items: flex-start;
        font-size: 18px;
    }
    .house-title-tag-box {
        margin-top: 5px;
    }

    .title-basic-info {
        flex-direction: column;
        align-items: flex-start;
    }
    .info-tag {
        margin-bottom: 5px;
        margin-right: 0;
        display: block;
    }
    .title-label {
        margin-left: 0;
        margin-top: 10px;
    }
    .report-btn, .report-phone {
        position: static;
        margin-top: 10px;
        text-align: left;
    }

    .house-info-zufang li {
        width: 100%; /* 每行一列 */
    }

    .peitao-item {
        width: 33.33%; /* 每行3个 */
    }
    .peitao-item:nth-child(4n) { /* 移除4列的右边框规则 */
        border-right: 1px solid #eee;
    }
    .peitao-item:nth-child(3n) { /* 每行3个的右边框规则 */
        border-right: none;
    }

    .ajax_prop li {
        width: 100%; /* 每行1个 */
    }

    .mod-title {
        flex-direction: column;
        align-items: flex-start;
    }
    .mod-title .right-info {
        margin-top: 5px;
    }
    .mod-title .mod-title-link {
        margin-left: 0;
        margin-top: 5px;
    }

    .scale_btn_wrap {
        flex-direction: row;
        top: auto;
        bottom: 10px;
        left: 50%;
        transform: translateX(-50%);
        width: auto;
    }
    .zoom_btn {
        border-bottom: none;
        border-right: 1px solid #eee;
    }
    .zoom_btn:last-child {
        border-right: none;
    }
}
</style>