<template>
   <div class="div-border items-list">
    <div class="items">
        <span class="region-title">位置</span>
        <span class="muti-row">
            <a class="region-item selected-item" href="https://cs.zu.anjuke.com/" title="区域租房">区域</a>
            <i class="region-arrow selected"></i>
            <a class="region-item" href="https://cs.zu.anjuke.com/ditie/" title="地铁租房">地铁</a>
            <i class="region-arrow"></i>
            <div class="sub-items sub-level1">
                <a href="https://cs.zu.anjuke.com/fangyuan/" title="全部租房" class="selected-item">全部</a>
                <a href="https://cs.zu.anjuke.com/fangyuan/yuelu/" title="岳麓租房">岳麓</a>
                <a href="https://cs.zu.anjuke.com/fangyuan/yuhuah/" title="雨花租房">雨花</a>
                <a href="https://cs.zu.anjuke.com/fangyuan/tianxin/" title="天心租房">天心</a>
                <a href="https://cs.zu.anjuke.com/fangyuan/kaifu/" title="开福租房">开福</a>
                <a href="https://cs.zu.anjuke.com/fangyuan/furong/" title="芙蓉租房">芙蓉</a>
                <a href="https://cs.zu.anjuke.com/fangyuan/xingshac/" title="星沙租房">星沙</a>
                <a href="https://cs.zu.anjuke.com/fangyuan/wangchenga/" title="望城租房">望城</a>
                <a href="https://cs.zu.anjuke.com/fangyuan/ningxiang/" title="宁乡租房">宁乡</a>
                <a href="https://cs.zu.anjuke.com/fangyuan/liuyang/" title="浏阳租房">浏阳</a>
                <a href="https://cs.zu.anjuke.com/fangyuan/xingsha/" title="长沙县租房">长沙县</a>
                <a href="" title="租房"></a>
            </div>
        </span>
    </div>
    <div class="items">
        <span class="muti-title">租金</span>
        <span class="muti-row">
            <a href="https://cs.zu.anjuke.com/fangyuan/" class="selected-item" rel="nofollow">全部</a>
            <a href="https://cs.zu.anjuke.com/fangyuan/zj201-tj1/" rel="nofollow">500元以下</a>
            <a href="https://cs.zu.anjuke.com/fangyuan/zj202-tj1/" rel="nofollow">500-800元</a>
            <a href="https://cs.zu.anjuke.com/fangyuan/zj203-tj1/" rel="nofollow">800-1000元</a>
            <a href="https://cs.zu.anjuke.com/fangyuan/zj204-tj1/" rel="nofollow">1000-1500元</a>
            <a href="https://cs.zu.anjuke.com/fangyuan/zj205-tj1/" rel="nofollow">1500-2000元</a>
            <a href="https://cs.zu.anjuke.com/fangyuan/zj206-tj1/" rel="nofollow">2000-3000元</a>
            <a href="https://cs.zu.anjuke.com/fangyuan/zj207-tj1/" rel="nofollow">3000-5000元</a>
            <a href="https://cs.zu.anjuke.com/fangyuan/zj208-tj1/" rel="nofollow">5000元以上</a>
            <div class="pricecond">
                <form action="https://cs.zu.anjuke.com/fangyuan/" id="price_range_form" onsubmit="">
                    <input id="from-price" class="from-price yell" type="text" name="from_price" maxlength="5" value="" autocomplete="off" style="width: 40px;">
                    <span class="sperate">-</span>
                    <input id="to-price" class="to-price yell" type="text" name="to_price" maxlength="5" value="" autocomplete="off" style="width: 40px;">
                    <span class="unit">元</span>
                    <input class="smit" id="pricerange_search" type="button" value="确定">
                </form>
            </div>
        </span>
    </div>

    <div class="items">
        <span class="item-title">房型</span>
        <span class="elems-l">
            <a href="https://cs.zu.anjuke.com/fangyuan/" class="selected-item" rel="nofollow">全部</a>
            <a href="https://cs.zu.anjuke.com/fangyuan/fx1/" rel="nofollow">一室</a>
            <a href="https://cs.zu.anjuke.com/fangyuan/fx2/" rel="nofollow">二室</a>
            <a href="https://cs.zu.anjuke.com/fangyuan/fx3/" rel="nofollow">三室</a>
            <a href="https://cs.zu.anjuke.com/fangyuan/fx4/" rel="nofollow">四室</a>
            <a href="https://cs.zu.anjuke.com/fangyuan/fx5/" rel="nofollow">五室及以上</a>
        </span>
    </div>

    <div class="items">
        <span class="item-title">类型</span>
        <span class="elems-l">
            <a href="https://cs.zu.anjuke.com/fangyuan/" class="selected-item" rel="nofollow">全部</a>
            <a href="https://cs.zu.anjuke.com/fangyuan/x1/" rel="nofollow">整租</a>
            <a href="https://cs.zu.anjuke.com/fangyuan/x2/" rel="nofollow">合租/单间</a>
        </span>
    </div>

    <div class="search_bottom clearfix">
        <div id="condmenu">
            <ul class="condul clearfix">
                <li class="condlist_tip"><span>更多</span></li>
                <li class="condibox">
                    <a class="cur-label" href="javascript:void(0);">
                        <span class="select_item">
                            <span class="txt" id="condhouseage_txt_id">房屋类型</span>
                        </span>
                    </a>
                    <ul style="display: none;">
                        <li class="selected">
                            <a href="https://cs.zu.anjuke.com/fangyuan/" rel="nofollow">全部</a>
                        </li>
                        <li>
                            <a href="https://cs.zu.anjuke.com/fangyuan/lx1/" rel="nofollow">普通住宅</a>
                        </li>
                        <li>
                            <a href="https://cs.zu.anjuke.com/fangyuan/lx4/" rel="nofollow">公寓</a>
                        </li>
                        <li>
                            <a href="https://cs.zu.anjuke.com/fangyuan/lx2/" rel="nofollow">别墅</a>
                        </li>
                        <li>
                            <a href="https://cs.zu.anjuke.com/fangyuan/lx9/" rel="nofollow">四合院</a>
                        </li>
                        <li>
                            <a href="https://cs.zu.anjuke.com/fangyuan/lx10/" rel="nofollow">商住两用</a>
                        </li>
                        <li>
                            <a href="https://cs.zu.anjuke.com/fangyuan/lx7/" rel="nofollow">新里洋房</a>
                        </li>
                        <li>
                            <a href="https://cs.zu.anjuke.com/fangyuan/lx8/" rel="nofollow">老公房</a>
                        </li>
                        <li>
                            <a href="https://cs.zu.anjuke.com/fangyuan/lx5/" rel="nofollow">平房</a>
                        </li>
                        <li>
                            <a href="https://cs.zu.anjuke.com/fangyuan/lx6/" rel="nofollow">其他</a>
                        </li>
                    </ul>
                </li>

                <li class="condibox">
                    <a class="cur-label" href="javascript:void(0);">
                        <span class="select_item">
                            <span class="txt" id="condhouse_orient_txt_id">朝向</span>
                        </span>
                    </a>
                    <ul style="display: none;">
                        <li class="selected">
                            <a href="https://cs.zu.anjuke.com/fangyuan/" rel="nofollow">全部</a>
                        </li>
                        <li>
                            <a href="https://cs.zu.anjuke.com/fangyuan/tw1/" rel="nofollow">朝东</a>
                        </li>
                        <li>
                            <a href="https://cs.zu.anjuke.com/fangyuan/tw2/" rel="nofollow">朝南</a>
                        </li>
                        <li>
                            <a href="https://cs.zu.anjuke.com/fangyuan/tw3/" rel="nofollow">朝西</a>
                        </li>
                        <li>
                            <a href="https://cs.zu.anjuke.com/fangyuan/tw4/" rel="nofollow">朝北</a>
                        </li>
                        <li>
                            <a href="https://cs.zu.anjuke.com/fangyuan/tw5/" rel="nofollow">东南</a>
                        </li>
                        <li>
                            <a href="https://cs.zu.anjuke.com/fangyuan/tw6/" rel="nofollow">东北</a>
                        </li>
                        <li>
                            <a href="https://cs.zu.anjuke.com/fangyuan/tw7/" rel="nofollow">西南</a>
                        </li>
                        <li>
                            <a href="https://cs.zu.anjuke.com/fangyuan/tw8/" rel="nofollow">西北</a>
                        </li>
                        <li>
                            <a href="https://cs.zu.anjuke.com/fangyuan/tw9/" rel="nofollow">南北</a>
                        </li>
                        <li>
                            <a href="https://cs.zu.anjuke.com/fangyuan/tw10/" rel="nofollow">东西</a>
                        </li>
                    </ul>
                </li>

                <li id="" class="filter_check">
                    <i class="check-box " href="javascript:" id="filter_is_school_input1" onclick="location.href='https://cs.zu.anjuke.com/fangyuan/mt1/'"></i>
                    <label for="filter_is_school_input1">近地铁</label>
                </li>
                <li id="" class="filter_check">
                    <i class="check-box " href="javascript:" id="filter_is_school_input2" onclick="location.href='https://cs.zu.anjuke.com/fangyuan/dtf1/'"></i>
                    <label for="filter_is_school_input2">电梯房</label>
                </li>
                <li id="" class="filter_check">
                    <i class="check-box " href="javascript:" id="filter_is_school_input3" onclick="location.href='https://cs.zu.anjuke.com/fangyuan/tj1/'"></i>
                    <label for="filter_is_school_input3">展示租金段中的被推荐房源</label>
                </li>
            </ul>
        </div>
    </div>
</div>
</template>
<style scoped>
.div-border {
    border: 1px solid #e0e0e0;
    margin-bottom: 20px;
    background-color: #fff;
    padding: 10px 0;
}

.items-list .items {
    padding-left: 20px;
    line-height: 48px;
    border-bottom: 1px dashed #e0e0e0;
    font-size: 14px;
    zoom: 1;
    overflow: hidden;
    position: relative;
}

.items-list .items:last-of-type {
    border-bottom: none;
}

.items-list .region-title,
.items-list .muti-title,
.items-list .item-title {
    float: left;
    color: #999;
    width: 60px;
    height: 48px;
    line-height: 48px;
    text-align: right;
    margin-right: 20px;
}

.items-list .muti-row,
.items-list .elems-l {
    float: left;
    width: calc(100% - 80px); /* Adjust width based on title width */
    position: relative;
}

.items-list .muti-row a,
.items-list .elems-l a {
    display: inline-block;
    padding: 0 12px;
    color: #333;
    margin-right: 10px;
    height: 28px;
    line-height: 28px;
    border-radius: 4px;
    margin-top: 10px;
    text-decoration: none;
}

.items-list .muti-row a.selected-item,
.items-list .elems-l a.selected-item {
    background-color: #00a581; /* Example green for selected */
    color: #fff;
}

.items-list .region-arrow {
    display: inline-block;
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #666;
    margin-left: 5px;
    vertical-align: middle;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.items-list .region-arrow.selected {
    border-top: 5px solid #00a581; /* Example green for selected arrow */
    transform: rotate(180deg);
}

.items-list .sub-items {
    padding-top: 10px;
    padding-bottom: 5px;
    border-top: 1px dashed #e0e0e0;
    margin-top: 10px;
    display: block; /* Sub-items are visible by default in the image */
}

.items-list .sub-items a {
    margin-right: 10px;
    margin-bottom: 5px;
    height: 28px;
    line-height: 28px;
}

/* Price input styling */
.pricecond {
    display: inline-block;
    margin-left: 20px;
    vertical-align: middle;
}

.pricecond form {
    display: flex;
    align-items: center;
}

.pricecond input[type="text"] {
    width: 60px; /* Adjust as needed */
    height: 28px;
    line-height: 28px;
    border: 1px solid #e0e0e0;
    padding: 0 5px;
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 14px;
    text-align: center;
}

.pricecond .sperate {
    margin: 0 5px;
    color: #999;
}

.pricecond .unit {
    margin-left: 5px;
    color: #999;
}

.pricecond .smit {
    background-color: #00a581;
    color: #fff;
    border: none;
    padding: 0 15px;
    height: 30px;
    line-height: 30px;
    border-radius: 4px;
    cursor: pointer;
    margin-left: 10px;
    font-size: 14px;
}

/* More filters section */
.search_bottom {
    padding: 0 20px;
    line-height: 48px;
    font-size: 14px;
}

.search_bottom .condul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex; /* Use flexbox for horizontal layout */
    align-items: center;
}

.search_bottom .condul li {
    margin-right: 20px;
    position: relative;
    cursor: pointer;
}

.search_bottom .condlist_tip {
    color: #999;
}

.search_bottom .condibox .cur-label {
    display: inline-flex;
    align-items: center;
    color: #333;
    text-decoration: none;
    padding: 0 5px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
}

.search_bottom .condibox .cur-label .txt {
    margin-right: 5px;
}

.search_bottom .condibox .cur-label::after {
    content: '';
    display: inline-block;
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid #666;
    margin-left: 5px;
    vertical-align: middle;
}

/* Dropdown list for "房屋类型" and "朝向" */
.search_bottom .condibox ul {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    z-index: 10;
    min-width: 120px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    list-style: none;
    margin: 0;
    padding: 5px 0;
    display: none; /* Hidden by default, toggled by JS */
}

.search_bottom .condibox ul li {
    margin: 0;
    padding: 0;
    line-height: normal;
}

.search_bottom .condibox ul li a {
    display: block;
    padding: 8px 15px;
    color: #333;
    text-decoration: none;
}

.search_bottom .condibox ul li a:hover {
    background-color: #f0f0f0;
}

.search_bottom .condibox ul li.selected a {
    color: #00a581; /* Selected item in dropdown */
    font-weight: bold;
}

/* Checkbox styling */
.filter_check {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
}

.filter_check .check-box {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 1px solid #ccc;
    border-radius: 3px;
    margin-right: 8px;
    cursor: pointer;
    vertical-align: middle;
}

.filter_check .check-box.checked {
    background-color: #00a581; /* Example checked state */
    border-color: #00a581;
    position: relative;
}

.filter_check .check-box.checked::after {
    content: '\2713'; /* Checkmark character */
    color: #fff;
    font-size: 12px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.filter_check label {
    cursor: pointer;
    color: #333;
    vertical-align: middle;
}

/* Utility classes */
.clearfix::before,
.clearfix::after {
    content: " ";
    display: table;
}

.clearfix::after {
    clear: both;
}

.clearfix {
    zoom: 1;
}
</style>