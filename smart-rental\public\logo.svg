<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe"/>
      <stop offset="100%" style="stop-color:#00f2fe"/>
    </linearGradient>
  </defs>
  <g transform="translate(56, 76)">
    <!-- House -->
    <path d="M200,20 L380,160 L380,360 L20,360 L20,160 L200,20z" 
          fill="url(#gradient1)" 
          stroke="#ffffff" 
          stroke-width="12"/>
    <!-- Heart -->
    <path d="M280,200 C280,160 310,140 340,140 C370,140 400,160 400,200 C400,280 280,340 280,340 C280,340 160,280 160,200 C160,160 190,140 220,140 C250,140 280,160 280,200z"
          fill="#ffffff"
          opacity="0.9"/>
  </g>
</svg> 