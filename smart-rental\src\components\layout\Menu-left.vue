<template>
  <div class="menu-left-container" :class="{ 'is-collapse': isCollapse }">
    <!-- Logo 和网站名称区域 -->
    <div class="logo-container">
      <img src="/src/assets/house.png" alt="寓见" class="logo-img">
      <h1 class="logo-title" v-show="!isCollapse">寓见</h1>
    </div>

    <el-menu
      default-active="2"
      class="el-menu-vertical"
      :collapse="isCollapse"
      background-color="#545c64"
      text-color="#fff"
      active-text-color="#ffd04b"
      @open="handleOpen"
      @close="handleClose"
      router
    >
      <el-menu-item index="demo">
        <el-icon><House /></el-icon>
        <span>首页</span>
      </el-menu-item>
      <el-menu-item index="lunbotu" >
        <el-icon><Search /></el-icon>
        <span>房源搜索</span>
      </el-menu-item>
      <el-sub-menu index="user">
        <template #title>
          <el-icon><Location /></el-icon>
          <span>区域房源</span>
        </template>
        <el-menu-item index="3-1">热门区域</el-menu-item>
        <el-menu-item index="3-2">优质学区</el-menu-item>
        <el-menu-item index="3-3">地铁沿线</el-menu-item>
      </el-sub-menu>
      <el-menu-item index="user">
        <el-icon><Star /></el-icon>
        <span>我的收藏</span>
      </el-menu-item>
      <el-menu-item index="search">
        <el-icon><Message /></el-icon>
        <span>消息中心</span>
      </el-menu-item>
      <el-menu-item index="6">
        <el-icon><Setting /></el-icon>
        <span>个人设置</span>
      </el-menu-item>
    </el-menu>
  </div>
</template>

<script lang="ts" setup>
import {
  House,
  Search,
  Location,
  Star,
  Message,
  Setting,
} from '@element-plus/icons-vue'
import { ref } from 'vue'
const isCollapse = ref(false)
const handleOpen = (key: string, keyPath: string[]) => {
  console.log(key, keyPath)
}
const handleClose = (key: string, keyPath: string[]) => {
  console.log(key, keyPath)
}

// 导出折叠状态，供父组件使用
defineExpose({
  isCollapse
})
</script>

<style scoped>
.menu-left-container {
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  transition: width 0.3s;
  width: var(--menu-width, 200px);
  background-color: #545c64;
}

.menu-left-container.is-collapse {
  width: var(--menu-collapsed-width, 64px);
}

.logo-container {
  height: var(--header-height, 100px);
  padding: 0 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  overflow: hidden;
  background-color: #4a5259;
  box-shadow: 0 1px 0 0 rgba(0, 0, 0, 0.1);
  position: relative;
}

.logo-img {
  height: 100px;
  width: 100px;
  flex-shrink: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #ffd04b;
  box-shadow: 0 0 15px rgba(255, 208, 75, 0.3);
  padding: 2px;
  background-color: white;
  transform-origin: center;
  position: relative;
  left: 0;
}

.is-collapse .logo-img {
  height: 45px;
  width: 45px;
  margin-left: -14px;
  border-width: 2px;
  transform: translateX(5px);
}

.logo-title {
  color: #fff;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  white-space: nowrap;
  opacity: 1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: "PingFang SC", "Microsoft YaHei", "微软雅黑", sans-serif;
  background: linear-gradient(120deg, #ffd04b 0%, #fff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 2px;
  text-shadow: 0 0 10px rgba(255, 208, 75, 0.3);
  position: absolute;
  left: 135px;
}

.is-collapse .logo-title {
  opacity: 0;
  transform: translateX(-20px);
}

.el-menu-vertical {
  border-right: none;
  height: calc(100% - var(--header-height, 60px));
}

.el-menu-vertical:not(.el-menu--collapse) {
  width: 100%;
}

/* 调整菜单项的字体大小 */
:deep(.el-menu-item),
:deep(.el-sub-menu__title) {
  font-size: 16px;
  height: 56px;
  line-height: 56px;
}

/* 调整图标大小 */
:deep(.el-menu-item .el-icon),
:deep(.el-sub-menu .el-icon) {
  width: 24px;
  height: 24px;
  margin-right: 12px;
}

/* 子菜单项样式 */
:deep(.el-menu--inline .el-menu-item) {
  font-size: 14px;
  height: 50px;
  line-height: 50px;
  padding-left: 48px !important;
}

/* 选中状态的样式 */
:deep(.el-menu-item.is-active) {
  background-color: #4a5259 !important;
  font-weight: 600;
}

/* 悬停效果 */
:deep(.el-menu-item:hover),
:deep(.el-sub-menu__title:hover) {
  background-color: #4a5259 !important;
}

/* 添加过渡效果 */
:deep(.el-menu-item),
:deep(.el-sub-menu__title) {
  transition: all 0.3s ease;
}
</style>
  